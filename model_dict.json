[{"name": "shizuku-local", "description": "Orange-Haired Girl, locally available. no internet required.", "url": "/live2d-models/shizuku/shizuku.model.json", "kScale": 0.5, "initialXshift": 0, "initialYshift": 0, "kXOffset": 1150, "idleMotionGroupName": "idle", "emotionMap": {"neutral": 0, "anger": 2, "disgust": 2, "fear": 1, "joy": 3, "smirk": 3, "sadness": 1, "surprise": 3}, "tapMotions": {"body": {"tap_body": 30, "shake": 30, "pinch_in": 20, "pinch_out": 20}, "head": {"flick_head": 40, "shake": 20, "pinch_in": 20, "pinch_out": 20}}}, {"name": "shi<PERSON>ku", "description": "Orange-Haired Girl. Will fetch from CDN.", "url": "https://cdn.jsdelivr.net/gh/guansss/pixi-live2d-display/test/assets/shizuku/shizuku.model.json", "kScale": 0.5, "initialXshift": 0, "initialYshift": 0, "kXOffset": 1150, "idleMotionGroupName": "idle", "emotionMap": {"neutral": 0, "anger": 2, "disgust": 2, "fear": 1, "joy": 3, "smirk": 3, "sadness": 1, "surprise": 3}}, {"name": "other_unit_90001", "description": "Knight Mommy", "url": "https://cdn.jsdelivr.net/gh/Eikanya/Live2d-model/VenusScramble/otherunits/other_unit_90001/live2d/model.json", "kScale": 0.3, "initialXshift": 0, "initialYshift": 0, "kXOffset": 1500, "kYOffset": -150, "idleMotionGroupName": "idle", "emotionMap": {"neutral": 0, "anger": 2, "disgust": 2, "fear": 3, "joy": 5, "sadness": 3, "surprise": 4}}, {"name": "player_unit_00003", "description": "Angel", "url": "https://cdn.jsdelivr.net/gh/Eikanya/Live2d-model/VenusScramble/playerunits/player_unit_00003/live2d/model.json", "kScale": 0.3, "initialXshift": 0, "initialYshift": 150, "kXOffset": 1500, "kYOffset": -50, "idleMotionGroupName": "idle", "emotionMap": {"neutral": 0, "anger": 2, "disgust": 2, "fear": 3, "joy": 5, "sadness": 3, "amusement": 7, "relief": 3, "remorse": 3, "caring": 5, "confusion": 3, "pride": 5, "optimism": 1, "excitement": 1}}, {"name": "ma<PERSON>ro", "description": "<PERSON><PERSON><PERSON>", "url": "https://cdn.jsdelivr.net/gh/artulloss/live2d/Sakurasou/mashiro/ryoufuku.model.json", "kScale": 0.3, "initialXshift": 0, "initialYshift": 0, "kXOffset": 1000, "kYOffset": -200, "idleMotionGroupName": "idle", "emotionMap": {"neutral": 0, "anger": 2, "disgust": 7, "fear": 3, "joy": 5, "sadness": 3, "surprise": 4, "admiration": 6, "amusement": 4, "relief": 4, "remorse": 3, "caring": 5, "confusion": 3, "curiosity": 4, "pride": 5, "realization": 4, "optimism": 1, "excitement": 6}}, {"name": "mao_pro", "description": "", "url": "/live2d-models/mao_pro/mao_pro.model3.json", "kScale": 0.3, "initialXshift": 0, "initialYshift": 0, "kXOffset": 1150, "idleMotionGroupName": "Idle", "emotionMap": {"neutral": 0, "anger": 2, "disgust": 2, "fear": 1, "joy": 3, "smirk": 3, "sadness": 1, "surprise": 3}, "tapMotions": {"HitAreaHead": {"": 1}, "HitAreaBody": {"": 1}}}]