---
name: Feature request / 功能建议
about: Suggest an idea for this project / 提出改善项目的建议
title: "[IDEA]"
labels: enhancement
assignees: ''

---

### 这个功能请求是用来解决什么问题的？ / Is your feature request related to a problem? Please describe.
*请清晰简洁地描述您遇到的问题。例如：我总是在 [...] 时感到不方便。*
*A clear and concise description of what the problem is. Ex. I'm always frustrated when [...] *

[在这里输入问题描述 / Type problem description here]

### 您期望的解决方案是什么？ / Describe the solution you'd like
*请清晰简洁地描述您希望实现的功能或效果。*
*A clear and concise description of what you want to happen.*

[在此处输入期望的解决方案 / Type desired solution here]

### 此功能为何对 Open-LLM-VTuber 很重要？ / Why is this important for Open-LLM-VTuber?
*请解释为什么这个功能对 Open-LLM-VTuber 项目来说是实用且重要的。它能带来什么价值？例如，它如何提升用户体验、扩展项目能力、解决核心痛点等。*
*Explain why this feature would be useful and significant for the Open-LLM-VTuber project. What value does it add? For example, how does it improve user experience, extend project capabilities, or solve core pain points?*

[在此处说明其重要性 / Explain its importance here]

### 您考虑过哪些替代方案？ / Describe alternatives you've considered
*请清晰简洁地描述您考虑过的任何替代解决方案或特性。*
*A clear and concise description of any alternative solutions or features you've considered.*

[在此处输入替代方案 / Type alternatives here]

### 您是否愿意参与开发此功能？ / Would you like to work on this issue?
*请回答 Yes 或 No。如果您愿意，我们可以讨论后续步骤。*
*Please answer Yes or No. If yes, we can discuss the next steps.*

[回答 Yes/No / Answer Yes/No]

### 补充信息 / Additional context
*在此处添加有关此功能请求的任何其他上下文、截图、日志或设计稿。*
*Add any other context, screenshots, logs, or mockups about the feature request here.*

[在此处添加补充信息 / Add additional context here]
