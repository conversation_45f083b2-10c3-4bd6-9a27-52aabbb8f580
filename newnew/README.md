# Advanced Multimodal Chatbot Application

A modern, real-time chatbot application with multimodal capabilities including speech, vision, and text processing.

## Features

- 🧠 **Multiple LLM Support**: Ollama (local), OpenAI, Claude, Gemini
- 🎤 **Speech-to-Text**: Real-time audio processing with multiple ASR engines
- 🔊 **Text-to-Speech**: Multiple TTS engines with voice customization
- 👁️ **Vision**: Image upload, camera capture, screen sharing
- 💾 **RAG System**: Document processing with vector search
- 🔄 **Real-time**: WebSocket-based streaming communication
- 💻 **Cross-platform**: Electron desktop app

## Architecture

```
Frontend (Electron + React + TypeScript)
    ↕ WebSocket + REST API
Backend (FastAPI + Python)
    ↕
AI Services (Ollama, OpenAI, etc.)
```

## Quick Start

### Prerequisites
- Node.js 18+
- Python 3.11+
- uv (Python package manager)
- Ollama (for local LLM)

### Installation

1. **Clone and setup**:
   ```bash
   cd newnew
   ```

2. **Backend setup**:
   ```bash
   cd backend
   uv sync
   uv run uvicorn src.main:app --reload --port 8000
   ```

3. **Frontend setup**:
   ```bash
   cd frontend
   npm install
   npm start
   ```

4. **Access the application**:
   - Web: http://localhost:3000
   - Desktop: Electron app will launch automatically

## Configuration

Edit `backend/config.yaml` to configure:
- LLM providers and models
- ASR/TTS engines
- RAG settings
- Audio parameters

## Development

- Backend: FastAPI with hot reload
- Frontend: React with hot reload
- WebSocket: Real-time bidirectional communication
- Database: SQLite (development) / PostgreSQL (production)

## Project Structure

```
newnew/
├── frontend/          # Electron + React app
├── backend/           # FastAPI server
├── shared/            # Shared types and schemas
├── docs/              # Documentation
└── README.md          # This file
```

## License

MIT License
