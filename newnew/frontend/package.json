{"name": "chatbot-frontend", "version": "0.1.0", "description": "Advanced multimodal chatbot frontend", "main": "public/electron.js", "homepage": "./", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.1", "@mui/material": "^5.15.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.68", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "axios": "^1.6.2", "electron-is-dev": "^3.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "react-scripts": "5.0.1", "typescript": "^4.9.5", "web-vitals": "^2.1.4", "zustand": "^4.4.7"}, "devDependencies": {"concurrently": "^8.2.2", "electron": "^27.1.3", "electron-builder": "^24.8.1", "wait-on": "^7.2.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "electron": "electron .", "electron-dev": "concurrently \"npm start\" \"wait-on http://localhost:3000 && electron .\"", "electron-pack": "npm run build && electron-builder", "preelectron-pack": "npm run build"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "build": {"appId": "com.chatbot.app", "productName": "Advanced Chatbot", "directories": {"output": "dist"}, "files": ["build/**/*", "public/electron.js", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}}