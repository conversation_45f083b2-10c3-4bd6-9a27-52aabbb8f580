const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App info
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  
  // File dialogs
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  
  // Menu events
  onMenuNewConversation: (callback) => ipcRenderer.on('menu-new-conversation', callback),
  onMenuOpenFile: (callback) => ipcRenderer.on('menu-open-file', callback),
  onMenuSettings: (callback) => ipcRenderer.on('menu-settings', callback),
  
  // Remove listeners
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel),
  
  // Platform info
  platform: process.platform,
  isElectron: true
});

// Expose a limited API for the renderer
contextBridge.exposeInMainWorld('chatbotAPI', {
  // Check if running in Electron
  isElectron: true,
  platform: process.platform,
  
  // File operations
  selectFile: async (options = {}) => {
    const defaultOptions = {
      properties: ['openFile'],
      filters: [
        { name: 'Documents', extensions: ['txt', 'pdf', 'docx'] },
        { name: 'Images', extensions: ['png', 'jpg', 'jpeg', 'gif', 'webp'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    };
    
    const result = await ipcRenderer.invoke('show-open-dialog', { ...defaultOptions, ...options });
    return result.canceled ? null : result.filePaths[0];
  },
  
  saveFile: async (options = {}) => {
    const result = await ipcRenderer.invoke('show-save-dialog', options);
    return result.canceled ? null : result.filePath;
  }
});
