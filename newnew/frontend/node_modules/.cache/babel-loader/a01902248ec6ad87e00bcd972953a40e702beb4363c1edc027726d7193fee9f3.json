{"ast": null, "code": "/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on = Emitter.prototype.addEventListener = function (event, fn) {\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || []).push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function (event, fn) {\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off = Emitter.prototype.removeListener = Emitter.prototype.removeAllListeners = Emitter.prototype.removeEventListener = function (event, fn) {\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function (event) {\n  this._callbacks = this._callbacks || {};\n  var args = new Array(arguments.length - 1),\n    callbacks = this._callbacks['$' + event];\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function (event) {\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function (event) {\n  return !!this.listeners(event).length;\n};", "map": {"version": 3, "names": ["Emitter", "obj", "mixin", "key", "prototype", "on", "addEventListener", "event", "fn", "_callbacks", "push", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "length", "callbacks", "cb", "i", "splice", "emit", "args", "Array", "slice", "len", "emit<PERSON><PERSON><PERSON><PERSON>", "listeners", "hasListeners"], "sources": ["/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/node_modules/@socket.io/component-emitter/lib/esm/index.js"], "sourcesContent": ["/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASA,OAAOA,CAACC,GAAG,EAAE;EAC3B,IAAIA,GAAG,EAAE,OAAOC,KAAK,CAACD,GAAG,CAAC;AAC5B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,KAAKA,CAACD,GAAG,EAAE;EAClB,KAAK,IAAIE,GAAG,IAAIH,OAAO,CAACI,SAAS,EAAE;IACjCH,GAAG,CAACE,GAAG,CAAC,GAAGH,OAAO,CAACI,SAAS,CAACD,GAAG,CAAC;EACnC;EACA,OAAOF,GAAG;AACZ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAD,OAAO,CAACI,SAAS,CAACC,EAAE,GACpBL,OAAO,CAACI,SAAS,CAACE,gBAAgB,GAAG,UAASC,KAAK,EAAEC,EAAE,EAAC;EACtD,IAAI,CAACC,UAAU,GAAG,IAAI,CAACA,UAAU,IAAI,CAAC,CAAC;EACvC,CAAC,IAAI,CAACA,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC,GAAG,IAAI,CAACE,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC,IAAI,EAAE,EAC/DG,IAAI,CAACF,EAAE,CAAC;EACX,OAAO,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAR,OAAO,CAACI,SAAS,CAACO,IAAI,GAAG,UAASJ,KAAK,EAAEC,EAAE,EAAC;EAC1C,SAASH,EAAEA,CAAA,EAAG;IACZ,IAAI,CAACO,GAAG,CAACL,KAAK,EAAEF,EAAE,CAAC;IACnBG,EAAE,CAACK,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC;EAC3B;EAEAT,EAAE,CAACG,EAAE,GAAGA,EAAE;EACV,IAAI,CAACH,EAAE,CAACE,KAAK,EAAEF,EAAE,CAAC;EAClB,OAAO,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAL,OAAO,CAACI,SAAS,CAACQ,GAAG,GACrBZ,OAAO,CAACI,SAAS,CAACW,cAAc,GAChCf,OAAO,CAACI,SAAS,CAACY,kBAAkB,GACpChB,OAAO,CAACI,SAAS,CAACa,mBAAmB,GAAG,UAASV,KAAK,EAAEC,EAAE,EAAC;EACzD,IAAI,CAACC,UAAU,GAAG,IAAI,CAACA,UAAU,IAAI,CAAC,CAAC;;EAEvC;EACA,IAAI,CAAC,IAAIK,SAAS,CAACI,MAAM,EAAE;IACzB,IAAI,CAACT,UAAU,GAAG,CAAC,CAAC;IACpB,OAAO,IAAI;EACb;;EAEA;EACA,IAAIU,SAAS,GAAG,IAAI,CAACV,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC;EAC5C,IAAI,CAACY,SAAS,EAAE,OAAO,IAAI;;EAE3B;EACA,IAAI,CAAC,IAAIL,SAAS,CAACI,MAAM,EAAE;IACzB,OAAO,IAAI,CAACT,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC;IACnC,OAAO,IAAI;EACb;;EAEA;EACA,IAAIa,EAAE;EACN,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,SAAS,CAACD,MAAM,EAAEG,CAAC,EAAE,EAAE;IACzCD,EAAE,GAAGD,SAAS,CAACE,CAAC,CAAC;IACjB,IAAID,EAAE,KAAKZ,EAAE,IAAIY,EAAE,CAACZ,EAAE,KAAKA,EAAE,EAAE;MAC7BW,SAAS,CAACG,MAAM,CAACD,CAAC,EAAE,CAAC,CAAC;MACtB;IACF;EACF;;EAEA;EACA;EACA,IAAIF,SAAS,CAACD,MAAM,KAAK,CAAC,EAAE;IAC1B,OAAO,IAAI,CAACT,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC;EACrC;EAEA,OAAO,IAAI;AACb,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAP,OAAO,CAACI,SAAS,CAACmB,IAAI,GAAG,UAAShB,KAAK,EAAC;EACtC,IAAI,CAACE,UAAU,GAAG,IAAI,CAACA,UAAU,IAAI,CAAC,CAAC;EAEvC,IAAIe,IAAI,GAAG,IAAIC,KAAK,CAACX,SAAS,CAACI,MAAM,GAAG,CAAC,CAAC;IACtCC,SAAS,GAAG,IAAI,CAACV,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC;EAE5C,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,SAAS,CAACI,MAAM,EAAEG,CAAC,EAAE,EAAE;IACzCG,IAAI,CAACH,CAAC,GAAG,CAAC,CAAC,GAAGP,SAAS,CAACO,CAAC,CAAC;EAC5B;EAEA,IAAIF,SAAS,EAAE;IACbA,SAAS,GAAGA,SAAS,CAACO,KAAK,CAAC,CAAC,CAAC;IAC9B,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEM,GAAG,GAAGR,SAAS,CAACD,MAAM,EAAEG,CAAC,GAAGM,GAAG,EAAE,EAAEN,CAAC,EAAE;MACpDF,SAAS,CAACE,CAAC,CAAC,CAACR,KAAK,CAAC,IAAI,EAAEW,IAAI,CAAC;IAChC;EACF;EAEA,OAAO,IAAI;AACb,CAAC;;AAED;AACAxB,OAAO,CAACI,SAAS,CAACwB,YAAY,GAAG5B,OAAO,CAACI,SAAS,CAACmB,IAAI;;AAEvD;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAvB,OAAO,CAACI,SAAS,CAACyB,SAAS,GAAG,UAAStB,KAAK,EAAC;EAC3C,IAAI,CAACE,UAAU,GAAG,IAAI,CAACA,UAAU,IAAI,CAAC,CAAC;EACvC,OAAO,IAAI,CAACA,UAAU,CAAC,GAAG,GAAGF,KAAK,CAAC,IAAI,EAAE;AAC3C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;;AAEAP,OAAO,CAACI,SAAS,CAAC0B,YAAY,GAAG,UAASvB,KAAK,EAAC;EAC9C,OAAO,CAAC,CAAE,IAAI,CAACsB,SAAS,CAACtB,KAAK,CAAC,CAACW,MAAM;AACxC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}