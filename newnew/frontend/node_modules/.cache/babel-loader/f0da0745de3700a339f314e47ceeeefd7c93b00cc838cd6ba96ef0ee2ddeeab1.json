{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M13.94 8.31C13.62 7.52 12.85 7 12 7s-1.62.52-1.94 1.31L7 16h3.5v6h3v-6H17z\"\n}, \"0\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"12\",\n  cy: \"4\",\n  r: \"2\"\n}, \"1\")], 'Woman2TwoTone');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d", "cx", "cy", "r"], "sources": ["/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/node_modules/@mui/icons-material/esm/Woman2TwoTone.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M13.94 8.31C13.62 7.52 12.85 7 12 7s-1.62.52-1.94 1.31L7 16h3.5v6h3v-6H17z\"\n}, \"0\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"12\",\n  cy: \"4\",\n  r: \"2\"\n}, \"1\")], 'Woman2TwoTone');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaD,IAAI,CAAC,QAAQ,EAAE;EACnCE,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,GAAG;EACPC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,eAAe,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}