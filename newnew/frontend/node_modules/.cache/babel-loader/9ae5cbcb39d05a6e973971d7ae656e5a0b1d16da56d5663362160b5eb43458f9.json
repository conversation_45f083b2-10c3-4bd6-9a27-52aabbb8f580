{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useEffect, useRef, useCallback, useState } from 'react';\nimport { useChatStore } from '../stores/chatStore';\nexport const useWebSocket = () => {\n  _s();\n  const socketRef = useRef(null);\n  const [isConnected, setIsConnected] = useState(false);\n  const [connectionError, setConnectionError] = useState(null);\n  const {\n    addMessage,\n    updateMessage,\n    setTyping,\n    setProcessingAudio,\n    setProviders,\n    currentConversationId,\n    selectedModel,\n    selectedProvider\n  } = useChatStore();\n  const connect = useCallback(() => {\n    var _socketRef$current;\n    if ((_socketRef$current = socketRef.current) !== null && _socketRef$current !== void 0 && _socketRef$current.connected) {\n      return;\n    }\n    const clientId = `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n\n    // Connect to WebSocket server\n    const socket = io(`ws://localhost:8000/ws/chat/${clientId}`, {\n      transports: ['websocket'],\n      upgrade: true\n    });\n    socketRef.current = socket;\n    socket.on('connect', () => {\n      console.log('Connected to WebSocket server');\n      setIsConnected(true);\n      setConnectionError(null);\n\n      // Request available models\n      socket.emit('message', {\n        type: 'config',\n        config_type: 'get_models'\n      });\n    });\n    socket.on('disconnect', () => {\n      console.log('Disconnected from WebSocket server');\n      setIsConnected(false);\n    });\n    socket.on('connect_error', error => {\n      console.error('WebSocket connection error:', error);\n      setConnectionError(error.message);\n      setIsConnected(false);\n    });\n    socket.on('message', data => {\n      try {\n        const message = JSON.parse(data);\n        handleMessage(message);\n      } catch (error) {\n        console.error('Failed to parse WebSocket message:', error);\n      }\n    });\n    return socket;\n  }, []);\n  const disconnect = useCallback(() => {\n    if (socketRef.current) {\n      socketRef.current.disconnect();\n      socketRef.current = null;\n      setIsConnected(false);\n    }\n  }, []);\n  const sendMessage = useCallback(message => {\n    var _socketRef$current2;\n    if ((_socketRef$current2 = socketRef.current) !== null && _socketRef$current2 !== void 0 && _socketRef$current2.connected) {\n      socketRef.current.emit('message', JSON.stringify(message));\n    } else {\n      console.error('WebSocket not connected');\n    }\n  }, []);\n  const handleMessage = useCallback(message => {\n    switch (message.type) {\n      case 'chat_chunk':\n        handleChatChunk(message);\n        break;\n      case 'chat_complete':\n        handleChatComplete(message);\n        break;\n      case 'transcription':\n        handleTranscription(message);\n        break;\n      case 'typing':\n        setTyping(message.status);\n        break;\n      case 'audio_processing':\n        setProcessingAudio(message.status);\n        break;\n      case 'config_response':\n        handleConfigResponse(message);\n        break;\n      case 'error':\n        handleError(message);\n        break;\n      case 'pong':\n        // Handle ping/pong for connection health\n        break;\n      default:\n        console.log('Unknown message type:', message.type);\n    }\n  }, [setTyping, setProcessingAudio]);\n  const handleChatChunk = useCallback(message => {\n    var _lastMessage$metadata;\n    const {\n      content,\n      conversation_id\n    } = message;\n    if (!conversation_id || !currentConversationId) return;\n\n    // Find or create assistant message\n    const conversation = useChatStore.getState().conversations.find(conv => conv.id === conversation_id);\n    if (!conversation) return;\n    const lastMessage = conversation.messages[conversation.messages.length - 1];\n    if (lastMessage && lastMessage.role === 'assistant' && (_lastMessage$metadata = lastMessage.metadata) !== null && _lastMessage$metadata !== void 0 && _lastMessage$metadata.processing) {\n      // Update existing message\n      updateMessage(lastMessage.id, {\n        content: lastMessage.content + content\n      });\n    } else {\n      // Create new assistant message\n      addMessage({\n        role: 'assistant',\n        content,\n        conversationId: conversation_id,\n        metadata: {\n          processing: true,\n          model: selectedModel,\n          provider: selectedProvider\n        }\n      });\n    }\n  }, [currentConversationId, selectedModel, selectedProvider, addMessage, updateMessage]);\n  const handleChatComplete = useCallback(message => {\n    var _lastMessage$metadata2;\n    const {\n      conversation_id\n    } = message;\n    if (!conversation_id) return;\n\n    // Mark the last assistant message as complete\n    const conversation = useChatStore.getState().conversations.find(conv => conv.id === conversation_id);\n    if (!conversation) return;\n    const lastMessage = conversation.messages[conversation.messages.length - 1];\n    if (lastMessage && lastMessage.role === 'assistant' && (_lastMessage$metadata2 = lastMessage.metadata) !== null && _lastMessage$metadata2 !== void 0 && _lastMessage$metadata2.processing) {\n      updateMessage(lastMessage.id, {\n        metadata: {\n          ...lastMessage.metadata,\n          processing: false\n        }\n      });\n    }\n    setTyping(false);\n  }, [updateMessage, setTyping]);\n  const handleTranscription = useCallback(message => {\n    const {\n      text\n    } = message;\n    if (text && currentConversationId) {\n      // Add user message from transcription\n      addMessage({\n        role: 'user',\n        content: text,\n        conversationId: currentConversationId,\n        metadata: {\n          audioTranscription: true\n        }\n      });\n    }\n  }, [currentConversationId, addMessage]);\n  const handleConfigResponse = useCallback(message => {\n    const {\n      config_type,\n      data\n    } = message;\n    if (config_type === 'models' && data) {\n      // Convert models data to providers format\n      const providersMap = new Map();\n      data.forEach(model => {\n        if (!providersMap.has(model.provider)) {\n          providersMap.set(model.provider, {\n            name: model.provider,\n            displayName: model.provider.charAt(0).toUpperCase() + model.provider.slice(1),\n            models: [],\n            available: true\n          });\n        }\n        providersMap.get(model.provider).models.push({\n          name: model.name,\n          displayName: model.display_name,\n          provider: model.provider,\n          maxTokens: model.max_tokens,\n          temperature: model.temperature,\n          available: model.available\n        });\n      });\n      setProviders(Array.from(providersMap.values()));\n    }\n  }, [setProviders]);\n  const handleError = useCallback(message => {\n    console.error('WebSocket error:', message.message);\n    setConnectionError(message.message);\n  }, []);\n\n  // Send chat message\n  const sendChatMessage = useCallback((content, images) => {\n    if (!currentConversationId) return;\n\n    // Add user message to store\n    addMessage({\n      role: 'user',\n      content,\n      conversationId: currentConversationId,\n      metadata: {\n        model: selectedModel,\n        provider: selectedProvider\n      }\n    });\n\n    // Send to server\n    sendMessage({\n      type: 'chat',\n      content,\n      conversation_id: currentConversationId,\n      model: selectedModel,\n      provider: selectedProvider,\n      images: images || []\n    });\n  }, [currentConversationId, selectedModel, selectedProvider, addMessage, sendMessage]);\n\n  // Send audio data\n  const sendAudioData = useCallback((audioData, format = 'wav') => {\n    if (!currentConversationId) return;\n    sendMessage({\n      type: 'audio',\n      audio_data: audioData,\n      format,\n      conversation_id: currentConversationId,\n      model: selectedModel,\n      provider: selectedProvider\n    });\n  }, [currentConversationId, selectedModel, selectedProvider, sendMessage]);\n\n  // Send ping to keep connection alive\n  useEffect(() => {\n    if (!isConnected) return;\n    const pingInterval = setInterval(() => {\n      sendMessage({\n        type: 'ping'\n      });\n    }, 30000); // Ping every 30 seconds\n\n    return () => clearInterval(pingInterval);\n  }, [isConnected, sendMessage]);\n  return {\n    connect,\n    disconnect,\n    sendChatMessage,\n    sendAudioData,\n    isConnected,\n    connectionError\n  };\n};\n_s(useWebSocket, \"smwNN9EzqluhodwNpplwG4UhWM4=\", false, function () {\n  return [useChatStore];\n});", "map": {"version": 3, "names": ["useEffect", "useRef", "useCallback", "useState", "useChatStore", "useWebSocket", "_s", "socketRef", "isConnected", "setIsConnected", "connectionError", "setConnectionError", "addMessage", "updateMessage", "setTyping", "setProcessingAudio", "setProviders", "currentConversationId", "selected<PERSON><PERSON>l", "<PERSON><PERSON><PERSON><PERSON>", "connect", "_socketRef$current", "current", "connected", "clientId", "Date", "now", "Math", "random", "toString", "substr", "socket", "io", "transports", "upgrade", "on", "console", "log", "emit", "type", "config_type", "error", "message", "data", "JSON", "parse", "handleMessage", "disconnect", "sendMessage", "_socketRef$current2", "stringify", "handleChatChunk", "handleChatComplete", "handleTranscription", "status", "handleConfigResponse", "handleError", "_lastMessage$metadata", "content", "conversation_id", "conversation", "getState", "conversations", "find", "conv", "id", "lastMessage", "messages", "length", "role", "metadata", "processing", "conversationId", "model", "provider", "_lastMessage$metadata2", "text", "audioTranscription", "providersMap", "Map", "for<PERSON>ach", "has", "set", "name", "displayName", "char<PERSON>t", "toUpperCase", "slice", "models", "available", "get", "push", "display_name", "maxTokens", "max_tokens", "temperature", "Array", "from", "values", "sendChatMessage", "images", "sendAudioData", "audioData", "format", "audio_data", "pingInterval", "setInterval", "clearInterval"], "sources": ["/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/src/hooks/useWebSocket.ts"], "sourcesContent": ["import { useEffect, useRef, useCallback, useState } from 'react';\nimport { useChatStore } from '../stores/chatStore';\n\ninterface WebSocketMessage {\n  type: string;\n  [key: string]: any;\n}\n\nexport const useWebSocket = () => {\n  const socketRef = useRef<WebSocket | null>(null);\n  const [isConnected, setIsConnected] = useState(false);\n  const [connectionError, setConnectionError] = useState<string | null>(null);\n  \n  const {\n    addMessage,\n    updateMessage,\n    setTyping,\n    setProcessingAudio,\n    setProviders,\n    currentConversationId,\n    selectedModel,\n    selectedProvider,\n  } = useChatStore();\n\n  const connect = useCallback(() => {\n    if (socketRef.current?.connected) {\n      return;\n    }\n\n    const clientId = `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    \n    // Connect to WebSocket server\n    const socket = io(`ws://localhost:8000/ws/chat/${clientId}`, {\n      transports: ['websocket'],\n      upgrade: true,\n    });\n\n    socketRef.current = socket;\n\n    socket.on('connect', () => {\n      console.log('Connected to WebSocket server');\n      setIsConnected(true);\n      setConnectionError(null);\n      \n      // Request available models\n      socket.emit('message', {\n        type: 'config',\n        config_type: 'get_models',\n      });\n    });\n\n    socket.on('disconnect', () => {\n      console.log('Disconnected from WebSocket server');\n      setIsConnected(false);\n    });\n\n    socket.on('connect_error', (error) => {\n      console.error('WebSocket connection error:', error);\n      setConnectionError(error.message);\n      setIsConnected(false);\n    });\n\n    socket.on('message', (data: string) => {\n      try {\n        const message: WebSocketMessage = JSON.parse(data);\n        handleMessage(message);\n      } catch (error) {\n        console.error('Failed to parse WebSocket message:', error);\n      }\n    });\n\n    return socket;\n  }, []);\n\n  const disconnect = useCallback(() => {\n    if (socketRef.current) {\n      socketRef.current.disconnect();\n      socketRef.current = null;\n      setIsConnected(false);\n    }\n  }, []);\n\n  const sendMessage = useCallback((message: WebSocketMessage) => {\n    if (socketRef.current?.connected) {\n      socketRef.current.emit('message', JSON.stringify(message));\n    } else {\n      console.error('WebSocket not connected');\n    }\n  }, []);\n\n  const handleMessage = useCallback((message: WebSocketMessage) => {\n    switch (message.type) {\n      case 'chat_chunk':\n        handleChatChunk(message);\n        break;\n      case 'chat_complete':\n        handleChatComplete(message);\n        break;\n      case 'transcription':\n        handleTranscription(message);\n        break;\n      case 'typing':\n        setTyping(message.status);\n        break;\n      case 'audio_processing':\n        setProcessingAudio(message.status);\n        break;\n      case 'config_response':\n        handleConfigResponse(message);\n        break;\n      case 'error':\n        handleError(message);\n        break;\n      case 'pong':\n        // Handle ping/pong for connection health\n        break;\n      default:\n        console.log('Unknown message type:', message.type);\n    }\n  }, [setTyping, setProcessingAudio]);\n\n  const handleChatChunk = useCallback((message: WebSocketMessage) => {\n    const { content, conversation_id } = message;\n    \n    if (!conversation_id || !currentConversationId) return;\n\n    // Find or create assistant message\n    const conversation = useChatStore.getState().conversations.find(\n      conv => conv.id === conversation_id\n    );\n    \n    if (!conversation) return;\n\n    const lastMessage = conversation.messages[conversation.messages.length - 1];\n    \n    if (lastMessage && lastMessage.role === 'assistant' && lastMessage.metadata?.processing) {\n      // Update existing message\n      updateMessage(lastMessage.id, {\n        content: lastMessage.content + content,\n      });\n    } else {\n      // Create new assistant message\n      addMessage({\n        role: 'assistant',\n        content,\n        conversationId: conversation_id,\n        metadata: {\n          processing: true,\n          model: selectedModel,\n          provider: selectedProvider,\n        },\n      });\n    }\n  }, [currentConversationId, selectedModel, selectedProvider, addMessage, updateMessage]);\n\n  const handleChatComplete = useCallback((message: WebSocketMessage) => {\n    const { conversation_id } = message;\n    \n    if (!conversation_id) return;\n\n    // Mark the last assistant message as complete\n    const conversation = useChatStore.getState().conversations.find(\n      conv => conv.id === conversation_id\n    );\n    \n    if (!conversation) return;\n\n    const lastMessage = conversation.messages[conversation.messages.length - 1];\n    \n    if (lastMessage && lastMessage.role === 'assistant' && lastMessage.metadata?.processing) {\n      updateMessage(lastMessage.id, {\n        metadata: {\n          ...lastMessage.metadata,\n          processing: false,\n        },\n      });\n    }\n    \n    setTyping(false);\n  }, [updateMessage, setTyping]);\n\n  const handleTranscription = useCallback((message: WebSocketMessage) => {\n    const { text } = message;\n    \n    if (text && currentConversationId) {\n      // Add user message from transcription\n      addMessage({\n        role: 'user',\n        content: text,\n        conversationId: currentConversationId,\n        metadata: {\n          audioTranscription: true,\n        },\n      });\n    }\n  }, [currentConversationId, addMessage]);\n\n  const handleConfigResponse = useCallback((message: WebSocketMessage) => {\n    const { config_type, data } = message;\n    \n    if (config_type === 'models' && data) {\n      // Convert models data to providers format\n      const providersMap = new Map();\n      \n      data.forEach((model: any) => {\n        if (!providersMap.has(model.provider)) {\n          providersMap.set(model.provider, {\n            name: model.provider,\n            displayName: model.provider.charAt(0).toUpperCase() + model.provider.slice(1),\n            models: [],\n            available: true,\n          });\n        }\n        \n        providersMap.get(model.provider).models.push({\n          name: model.name,\n          displayName: model.display_name,\n          provider: model.provider,\n          maxTokens: model.max_tokens,\n          temperature: model.temperature,\n          available: model.available,\n        });\n      });\n      \n      setProviders(Array.from(providersMap.values()));\n    }\n  }, [setProviders]);\n\n  const handleError = useCallback((message: WebSocketMessage) => {\n    console.error('WebSocket error:', message.message);\n    setConnectionError(message.message);\n  }, []);\n\n  // Send chat message\n  const sendChatMessage = useCallback((content: string, images?: string[]) => {\n    if (!currentConversationId) return;\n\n    // Add user message to store\n    addMessage({\n      role: 'user',\n      content,\n      conversationId: currentConversationId,\n      metadata: {\n        model: selectedModel,\n        provider: selectedProvider,\n      },\n    });\n\n    // Send to server\n    sendMessage({\n      type: 'chat',\n      content,\n      conversation_id: currentConversationId,\n      model: selectedModel,\n      provider: selectedProvider,\n      images: images || [],\n    });\n  }, [currentConversationId, selectedModel, selectedProvider, addMessage, sendMessage]);\n\n  // Send audio data\n  const sendAudioData = useCallback((audioData: string, format: string = 'wav') => {\n    if (!currentConversationId) return;\n\n    sendMessage({\n      type: 'audio',\n      audio_data: audioData,\n      format,\n      conversation_id: currentConversationId,\n      model: selectedModel,\n      provider: selectedProvider,\n    });\n  }, [currentConversationId, selectedModel, selectedProvider, sendMessage]);\n\n  // Send ping to keep connection alive\n  useEffect(() => {\n    if (!isConnected) return;\n\n    const pingInterval = setInterval(() => {\n      sendMessage({ type: 'ping' });\n    }, 30000); // Ping every 30 seconds\n\n    return () => clearInterval(pingInterval);\n  }, [isConnected, sendMessage]);\n\n  return {\n    connect,\n    disconnect,\n    sendChatMessage,\n    sendAudioData,\n    isConnected,\n    connectionError,\n  };\n};\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,OAAO;AAChE,SAASC,YAAY,QAAQ,qBAAqB;AAOlD,OAAO,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAMC,SAAS,GAAGN,MAAM,CAAmB,IAAI,CAAC;EAChD,MAAM,CAACO,WAAW,EAAEC,cAAc,CAAC,GAAGN,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACO,eAAe,EAAEC,kBAAkB,CAAC,GAAGR,QAAQ,CAAgB,IAAI,CAAC;EAE3E,MAAM;IACJS,UAAU;IACVC,aAAa;IACbC,SAAS;IACTC,kBAAkB;IAClBC,YAAY;IACZC,qBAAqB;IACrBC,aAAa;IACbC;EACF,CAAC,GAAGf,YAAY,CAAC,CAAC;EAElB,MAAMgB,OAAO,GAAGlB,WAAW,CAAC,MAAM;IAAA,IAAAmB,kBAAA;IAChC,KAAAA,kBAAA,GAAId,SAAS,CAACe,OAAO,cAAAD,kBAAA,eAAjBA,kBAAA,CAAmBE,SAAS,EAAE;MAChC;IACF;IAEA,MAAMC,QAAQ,GAAG,UAAUC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;;IAElF;IACA,MAAMC,MAAM,GAAGC,EAAE,CAAC,+BAA+BR,QAAQ,EAAE,EAAE;MAC3DS,UAAU,EAAE,CAAC,WAAW,CAAC;MACzBC,OAAO,EAAE;IACX,CAAC,CAAC;IAEF3B,SAAS,CAACe,OAAO,GAAGS,MAAM;IAE1BA,MAAM,CAACI,EAAE,CAAC,SAAS,EAAE,MAAM;MACzBC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C5B,cAAc,CAAC,IAAI,CAAC;MACpBE,kBAAkB,CAAC,IAAI,CAAC;;MAExB;MACAoB,MAAM,CAACO,IAAI,CAAC,SAAS,EAAE;QACrBC,IAAI,EAAE,QAAQ;QACdC,WAAW,EAAE;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFT,MAAM,CAACI,EAAE,CAAC,YAAY,EAAE,MAAM;MAC5BC,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjD5B,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,CAAC;IAEFsB,MAAM,CAACI,EAAE,CAAC,eAAe,EAAGM,KAAK,IAAK;MACpCL,OAAO,CAACK,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD9B,kBAAkB,CAAC8B,KAAK,CAACC,OAAO,CAAC;MACjCjC,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC,CAAC;IAEFsB,MAAM,CAACI,EAAE,CAAC,SAAS,EAAGQ,IAAY,IAAK;MACrC,IAAI;QACF,MAAMD,OAAyB,GAAGE,IAAI,CAACC,KAAK,CAACF,IAAI,CAAC;QAClDG,aAAa,CAACJ,OAAO,CAAC;MACxB,CAAC,CAAC,OAAOD,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC5D;IACF,CAAC,CAAC;IAEF,OAAOV,MAAM;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMgB,UAAU,GAAG7C,WAAW,CAAC,MAAM;IACnC,IAAIK,SAAS,CAACe,OAAO,EAAE;MACrBf,SAAS,CAACe,OAAO,CAACyB,UAAU,CAAC,CAAC;MAC9BxC,SAAS,CAACe,OAAO,GAAG,IAAI;MACxBb,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMuC,WAAW,GAAG9C,WAAW,CAAEwC,OAAyB,IAAK;IAAA,IAAAO,mBAAA;IAC7D,KAAAA,mBAAA,GAAI1C,SAAS,CAACe,OAAO,cAAA2B,mBAAA,eAAjBA,mBAAA,CAAmB1B,SAAS,EAAE;MAChChB,SAAS,CAACe,OAAO,CAACgB,IAAI,CAAC,SAAS,EAAEM,IAAI,CAACM,SAAS,CAACR,OAAO,CAAC,CAAC;IAC5D,CAAC,MAAM;MACLN,OAAO,CAACK,KAAK,CAAC,yBAAyB,CAAC;IAC1C;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,aAAa,GAAG5C,WAAW,CAAEwC,OAAyB,IAAK;IAC/D,QAAQA,OAAO,CAACH,IAAI;MAClB,KAAK,YAAY;QACfY,eAAe,CAACT,OAAO,CAAC;QACxB;MACF,KAAK,eAAe;QAClBU,kBAAkB,CAACV,OAAO,CAAC;QAC3B;MACF,KAAK,eAAe;QAClBW,mBAAmB,CAACX,OAAO,CAAC;QAC5B;MACF,KAAK,QAAQ;QACX5B,SAAS,CAAC4B,OAAO,CAACY,MAAM,CAAC;QACzB;MACF,KAAK,kBAAkB;QACrBvC,kBAAkB,CAAC2B,OAAO,CAACY,MAAM,CAAC;QAClC;MACF,KAAK,iBAAiB;QACpBC,oBAAoB,CAACb,OAAO,CAAC;QAC7B;MACF,KAAK,OAAO;QACVc,WAAW,CAACd,OAAO,CAAC;QACpB;MACF,KAAK,MAAM;QACT;QACA;MACF;QACEN,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEK,OAAO,CAACH,IAAI,CAAC;IACtD;EACF,CAAC,EAAE,CAACzB,SAAS,EAAEC,kBAAkB,CAAC,CAAC;EAEnC,MAAMoC,eAAe,GAAGjD,WAAW,CAAEwC,OAAyB,IAAK;IAAA,IAAAe,qBAAA;IACjE,MAAM;MAAEC,OAAO;MAAEC;IAAgB,CAAC,GAAGjB,OAAO;IAE5C,IAAI,CAACiB,eAAe,IAAI,CAAC1C,qBAAqB,EAAE;;IAEhD;IACA,MAAM2C,YAAY,GAAGxD,YAAY,CAACyD,QAAQ,CAAC,CAAC,CAACC,aAAa,CAACC,IAAI,CAC7DC,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKN,eACtB,CAAC;IAED,IAAI,CAACC,YAAY,EAAE;IAEnB,MAAMM,WAAW,GAAGN,YAAY,CAACO,QAAQ,CAACP,YAAY,CAACO,QAAQ,CAACC,MAAM,GAAG,CAAC,CAAC;IAE3E,IAAIF,WAAW,IAAIA,WAAW,CAACG,IAAI,KAAK,WAAW,KAAAZ,qBAAA,GAAIS,WAAW,CAACI,QAAQ,cAAAb,qBAAA,eAApBA,qBAAA,CAAsBc,UAAU,EAAE;MACvF;MACA1D,aAAa,CAACqD,WAAW,CAACD,EAAE,EAAE;QAC5BP,OAAO,EAAEQ,WAAW,CAACR,OAAO,GAAGA;MACjC,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA9C,UAAU,CAAC;QACTyD,IAAI,EAAE,WAAW;QACjBX,OAAO;QACPc,cAAc,EAAEb,eAAe;QAC/BW,QAAQ,EAAE;UACRC,UAAU,EAAE,IAAI;UAChBE,KAAK,EAAEvD,aAAa;UACpBwD,QAAQ,EAAEvD;QACZ;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACF,qBAAqB,EAAEC,aAAa,EAAEC,gBAAgB,EAAEP,UAAU,EAAEC,aAAa,CAAC,CAAC;EAEvF,MAAMuC,kBAAkB,GAAGlD,WAAW,CAAEwC,OAAyB,IAAK;IAAA,IAAAiC,sBAAA;IACpE,MAAM;MAAEhB;IAAgB,CAAC,GAAGjB,OAAO;IAEnC,IAAI,CAACiB,eAAe,EAAE;;IAEtB;IACA,MAAMC,YAAY,GAAGxD,YAAY,CAACyD,QAAQ,CAAC,CAAC,CAACC,aAAa,CAACC,IAAI,CAC7DC,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKN,eACtB,CAAC;IAED,IAAI,CAACC,YAAY,EAAE;IAEnB,MAAMM,WAAW,GAAGN,YAAY,CAACO,QAAQ,CAACP,YAAY,CAACO,QAAQ,CAACC,MAAM,GAAG,CAAC,CAAC;IAE3E,IAAIF,WAAW,IAAIA,WAAW,CAACG,IAAI,KAAK,WAAW,KAAAM,sBAAA,GAAIT,WAAW,CAACI,QAAQ,cAAAK,sBAAA,eAApBA,sBAAA,CAAsBJ,UAAU,EAAE;MACvF1D,aAAa,CAACqD,WAAW,CAACD,EAAE,EAAE;QAC5BK,QAAQ,EAAE;UACR,GAAGJ,WAAW,CAACI,QAAQ;UACvBC,UAAU,EAAE;QACd;MACF,CAAC,CAAC;IACJ;IAEAzD,SAAS,CAAC,KAAK,CAAC;EAClB,CAAC,EAAE,CAACD,aAAa,EAAEC,SAAS,CAAC,CAAC;EAE9B,MAAMuC,mBAAmB,GAAGnD,WAAW,CAAEwC,OAAyB,IAAK;IACrE,MAAM;MAAEkC;IAAK,CAAC,GAAGlC,OAAO;IAExB,IAAIkC,IAAI,IAAI3D,qBAAqB,EAAE;MACjC;MACAL,UAAU,CAAC;QACTyD,IAAI,EAAE,MAAM;QACZX,OAAO,EAAEkB,IAAI;QACbJ,cAAc,EAAEvD,qBAAqB;QACrCqD,QAAQ,EAAE;UACRO,kBAAkB,EAAE;QACtB;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC5D,qBAAqB,EAAEL,UAAU,CAAC,CAAC;EAEvC,MAAM2C,oBAAoB,GAAGrD,WAAW,CAAEwC,OAAyB,IAAK;IACtE,MAAM;MAAEF,WAAW;MAAEG;IAAK,CAAC,GAAGD,OAAO;IAErC,IAAIF,WAAW,KAAK,QAAQ,IAAIG,IAAI,EAAE;MACpC;MACA,MAAMmC,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;MAE9BpC,IAAI,CAACqC,OAAO,CAAEP,KAAU,IAAK;QAC3B,IAAI,CAACK,YAAY,CAACG,GAAG,CAACR,KAAK,CAACC,QAAQ,CAAC,EAAE;UACrCI,YAAY,CAACI,GAAG,CAACT,KAAK,CAACC,QAAQ,EAAE;YAC/BS,IAAI,EAAEV,KAAK,CAACC,QAAQ;YACpBU,WAAW,EAAEX,KAAK,CAACC,QAAQ,CAACW,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGb,KAAK,CAACC,QAAQ,CAACa,KAAK,CAAC,CAAC,CAAC;YAC7EC,MAAM,EAAE,EAAE;YACVC,SAAS,EAAE;UACb,CAAC,CAAC;QACJ;QAEAX,YAAY,CAACY,GAAG,CAACjB,KAAK,CAACC,QAAQ,CAAC,CAACc,MAAM,CAACG,IAAI,CAAC;UAC3CR,IAAI,EAAEV,KAAK,CAACU,IAAI;UAChBC,WAAW,EAAEX,KAAK,CAACmB,YAAY;UAC/BlB,QAAQ,EAAED,KAAK,CAACC,QAAQ;UACxBmB,SAAS,EAAEpB,KAAK,CAACqB,UAAU;UAC3BC,WAAW,EAAEtB,KAAK,CAACsB,WAAW;UAC9BN,SAAS,EAAEhB,KAAK,CAACgB;QACnB,CAAC,CAAC;MACJ,CAAC,CAAC;MAEFzE,YAAY,CAACgF,KAAK,CAACC,IAAI,CAACnB,YAAY,CAACoB,MAAM,CAAC,CAAC,CAAC,CAAC;IACjD;EACF,CAAC,EAAE,CAAClF,YAAY,CAAC,CAAC;EAElB,MAAMwC,WAAW,GAAGtD,WAAW,CAAEwC,OAAyB,IAAK;IAC7DN,OAAO,CAACK,KAAK,CAAC,kBAAkB,EAAEC,OAAO,CAACA,OAAO,CAAC;IAClD/B,kBAAkB,CAAC+B,OAAO,CAACA,OAAO,CAAC;EACrC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMyD,eAAe,GAAGjG,WAAW,CAAC,CAACwD,OAAe,EAAE0C,MAAiB,KAAK;IAC1E,IAAI,CAACnF,qBAAqB,EAAE;;IAE5B;IACAL,UAAU,CAAC;MACTyD,IAAI,EAAE,MAAM;MACZX,OAAO;MACPc,cAAc,EAAEvD,qBAAqB;MACrCqD,QAAQ,EAAE;QACRG,KAAK,EAAEvD,aAAa;QACpBwD,QAAQ,EAAEvD;MACZ;IACF,CAAC,CAAC;;IAEF;IACA6B,WAAW,CAAC;MACVT,IAAI,EAAE,MAAM;MACZmB,OAAO;MACPC,eAAe,EAAE1C,qBAAqB;MACtCwD,KAAK,EAAEvD,aAAa;MACpBwD,QAAQ,EAAEvD,gBAAgB;MAC1BiF,MAAM,EAAEA,MAAM,IAAI;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACnF,qBAAqB,EAAEC,aAAa,EAAEC,gBAAgB,EAAEP,UAAU,EAAEoC,WAAW,CAAC,CAAC;;EAErF;EACA,MAAMqD,aAAa,GAAGnG,WAAW,CAAC,CAACoG,SAAiB,EAAEC,MAAc,GAAG,KAAK,KAAK;IAC/E,IAAI,CAACtF,qBAAqB,EAAE;IAE5B+B,WAAW,CAAC;MACVT,IAAI,EAAE,OAAO;MACbiE,UAAU,EAAEF,SAAS;MACrBC,MAAM;MACN5C,eAAe,EAAE1C,qBAAqB;MACtCwD,KAAK,EAAEvD,aAAa;MACpBwD,QAAQ,EAAEvD;IACZ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACF,qBAAqB,EAAEC,aAAa,EAAEC,gBAAgB,EAAE6B,WAAW,CAAC,CAAC;;EAEzE;EACAhD,SAAS,CAAC,MAAM;IACd,IAAI,CAACQ,WAAW,EAAE;IAElB,MAAMiG,YAAY,GAAGC,WAAW,CAAC,MAAM;MACrC1D,WAAW,CAAC;QAAET,IAAI,EAAE;MAAO,CAAC,CAAC;IAC/B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAMoE,aAAa,CAACF,YAAY,CAAC;EAC1C,CAAC,EAAE,CAACjG,WAAW,EAAEwC,WAAW,CAAC,CAAC;EAE9B,OAAO;IACL5B,OAAO;IACP2B,UAAU;IACVoD,eAAe;IACfE,aAAa;IACb7F,WAAW;IACXE;EACF,CAAC;AACH,CAAC;AAACJ,EAAA,CA5RWD,YAAY;EAAA,QAcnBD,YAAY;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}