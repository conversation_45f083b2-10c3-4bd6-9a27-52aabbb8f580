{"ast": null, "code": "\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M4 3h6v2H4zM1 7h5v2H1zm2 12h5v2H3zm12.32-6.91 5.42-9.04L17.32 1 12 5.97v4.74c.31-.13.64-.21 1-.21 1.06 0 1.96.66 2.32 1.59M10.5 13c0-.82.4-1.54 1.01-2H1v4l7 2 3.44-2.06c-.57-.46-.94-1.15-.94-1.94m9.67 10L23 20.17l-3.54-6.36-3.98-1c0 .06.02.12.02.19 0 1.38-1.12 2.5-2.5 2.5-.36 0-.69-.08-1-.21V21c-1.1 0-2 .9-2 2h6c0-1.1-.9-2-2-2v-4.17z\"\n}, \"0\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"13\",\n  cy: \"13\",\n  r: \"1.5\"\n}, \"1\")], '<PERSON><PERSON>owerSharp');", "map": {"version": 3, "names": ["createSvgIcon", "jsx", "_jsx", "d", "cx", "cy", "r"], "sources": ["/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/node_modules/@mui/icons-material/esm/WindPowerSharp.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from './utils/createSvgIcon';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M4 3h6v2H4zM1 7h5v2H1zm2 12h5v2H3zm12.32-6.91 5.42-9.04L17.32 1 12 5.97v4.74c.31-.13.64-.21 1-.21 1.06 0 1.96.66 2.32 1.59M10.5 13c0-.82.4-1.54 1.01-2H1v4l7 2 3.44-2.06c-.57-.46-.94-1.15-.94-1.94m9.67 10L23 20.17l-3.54-6.36-3.98-1c0 .06.02.12.02.19 0 1.38-1.12 2.5-2.5 2.5-.36 0-.69-.08-1-.21V21c-1.1 0-2 .9-2 2h6c0-1.1-.9-2-2-2v-4.17z\"\n}, \"0\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"13\",\n  cy: \"13\",\n  r: \"1.5\"\n}, \"1\")], '<PERSON><PERSON>owerSharp');"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,uBAAuB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAeF,aAAa,CAAC,CAAC,aAAaE,IAAI,CAAC,MAAM,EAAE;EACtDC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,EAAE,aAAaD,IAAI,CAAC,QAAQ,EAAE;EACnCE,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,CAAC,EAAE;AACL,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,gBAAgB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}