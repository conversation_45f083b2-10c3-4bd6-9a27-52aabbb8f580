{"ast": null, "code": "export default function mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}", "map": {"version": 3, "names": ["mergeByName", "modifiers", "merged", "reduce", "current", "existing", "name", "Object", "assign", "options", "data", "keys", "map", "key"], "sources": ["/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/node_modules/@popperjs/core/lib/utils/mergeByName.js"], "sourcesContent": ["export default function mergeByName(modifiers) {\n  var merged = modifiers.reduce(function (merged, current) {\n    var existing = merged[current.name];\n    merged[current.name] = existing ? Object.assign({}, existing, current, {\n      options: Object.assign({}, existing.options, current.options),\n      data: Object.assign({}, existing.data, current.data)\n    }) : current;\n    return merged;\n  }, {}); // IE11 does not support Object.values\n\n  return Object.keys(merged).map(function (key) {\n    return merged[key];\n  });\n}"], "mappings": "AAAA,eAAe,SAASA,WAAWA,CAACC,SAAS,EAAE;EAC7C,IAAIC,MAAM,GAAGD,SAAS,CAACE,MAAM,CAAC,UAAUD,MAAM,EAAEE,OAAO,EAAE;IACvD,IAAIC,QAAQ,GAAGH,MAAM,CAACE,OAAO,CAACE,IAAI,CAAC;IACnCJ,MAAM,CAACE,OAAO,CAACE,IAAI,CAAC,GAAGD,QAAQ,GAAGE,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,QAAQ,EAAED,OAAO,EAAE;MACrEK,OAAO,EAAEF,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,QAAQ,CAACI,OAAO,EAAEL,OAAO,CAACK,OAAO,CAAC;MAC7DC,IAAI,EAAEH,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,QAAQ,CAACK,IAAI,EAAEN,OAAO,CAACM,IAAI;IACrD,CAAC,CAAC,GAAGN,OAAO;IACZ,OAAOF,MAAM;EACf,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;;EAER,OAAOK,MAAM,CAACI,IAAI,CAACT,MAAM,CAAC,CAACU,GAAG,CAAC,UAAUC,GAAG,EAAE;IAC5C,OAAOX,MAAM,CAACW,GAAG,CAAC;EACpB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}