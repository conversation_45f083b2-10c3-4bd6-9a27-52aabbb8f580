{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useEffect, useRef, useCallback, useState } from 'react';\nimport { useChatStore } from '../stores/chatStore';\nexport const useWebSocket = () => {\n  _s();\n  const socketRef = useRef(null);\n  const [isConnected, setIsConnected] = useState(false);\n  const [connectionError, setConnectionError] = useState(null);\n  const {\n    addMessage,\n    updateMessage,\n    setTyping,\n    setProcessingAudio,\n    setProviders,\n    currentConversationId,\n    selectedModel,\n    selectedProvider\n  } = useChatStore();\n  const connect = useCallback(() => {\n    var _socketRef$current;\n    if (((_socketRef$current = socketRef.current) === null || _socketRef$current === void 0 ? void 0 : _socketRef$current.readyState) === WebSocket.OPEN) {\n      return;\n    }\n    const clientId = `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n\n    // Connect to WebSocket server\n    const socket = new WebSocket(`ws://localhost:8000/ws/chat/${clientId}`);\n    socketRef.current = socket;\n    socket.onopen = () => {\n      console.log('Connected to WebSocket server');\n      setIsConnected(true);\n      setConnectionError(null);\n\n      // Request available models\n      socket.send(JSON.stringify({\n        type: 'config',\n        config_type: 'get_models'\n      }));\n    };\n    socket.onclose = () => {\n      console.log('Disconnected from WebSocket server');\n      setIsConnected(false);\n    };\n    socket.onerror = error => {\n      console.error('WebSocket connection error:', error);\n      setConnectionError('Connection failed');\n      setIsConnected(false);\n    };\n    socket.onmessage = event => {\n      try {\n        const message = JSON.parse(event.data);\n        handleMessage(message);\n      } catch (error) {\n        console.error('Failed to parse WebSocket message:', error);\n      }\n    };\n    return socket;\n  }, []);\n  const disconnect = useCallback(() => {\n    if (socketRef.current) {\n      socketRef.current.close();\n      socketRef.current = null;\n      setIsConnected(false);\n    }\n  }, []);\n  const sendMessage = useCallback(message => {\n    var _socketRef$current2;\n    if (((_socketRef$current2 = socketRef.current) === null || _socketRef$current2 === void 0 ? void 0 : _socketRef$current2.readyState) === WebSocket.OPEN) {\n      socketRef.current.send(JSON.stringify(message));\n    } else {\n      console.error('WebSocket not connected');\n    }\n  }, []);\n  const handleMessage = useCallback(message => {\n    switch (message.type) {\n      case 'chat_chunk':\n        handleChatChunk(message);\n        break;\n      case 'chat_complete':\n        handleChatComplete(message);\n        break;\n      case 'transcription':\n        handleTranscription(message);\n        break;\n      case 'typing':\n        setTyping(message.status);\n        break;\n      case 'audio_processing':\n        setProcessingAudio(message.status);\n        break;\n      case 'config_response':\n        handleConfigResponse(message);\n        break;\n      case 'error':\n        handleError(message);\n        break;\n      case 'pong':\n        // Handle ping/pong for connection health\n        break;\n      default:\n        console.log('Unknown message type:', message.type);\n    }\n  }, [setTyping, setProcessingAudio]);\n  const handleChatChunk = useCallback(message => {\n    var _lastMessage$metadata;\n    const {\n      content,\n      conversation_id\n    } = message;\n    if (!conversation_id || !currentConversationId) return;\n\n    // Find or create assistant message\n    const conversation = useChatStore.getState().conversations.find(conv => conv.id === conversation_id);\n    if (!conversation) return;\n    const lastMessage = conversation.messages[conversation.messages.length - 1];\n    if (lastMessage && lastMessage.role === 'assistant' && (_lastMessage$metadata = lastMessage.metadata) !== null && _lastMessage$metadata !== void 0 && _lastMessage$metadata.processing) {\n      // Update existing message\n      updateMessage(lastMessage.id, {\n        content: lastMessage.content + content\n      });\n    } else {\n      // Create new assistant message\n      addMessage({\n        role: 'assistant',\n        content,\n        conversationId: conversation_id,\n        metadata: {\n          processing: true,\n          model: selectedModel,\n          provider: selectedProvider\n        }\n      });\n    }\n  }, [currentConversationId, selectedModel, selectedProvider, addMessage, updateMessage]);\n  const handleChatComplete = useCallback(message => {\n    var _lastMessage$metadata2;\n    const {\n      conversation_id\n    } = message;\n    if (!conversation_id) return;\n\n    // Mark the last assistant message as complete\n    const conversation = useChatStore.getState().conversations.find(conv => conv.id === conversation_id);\n    if (!conversation) return;\n    const lastMessage = conversation.messages[conversation.messages.length - 1];\n    if (lastMessage && lastMessage.role === 'assistant' && (_lastMessage$metadata2 = lastMessage.metadata) !== null && _lastMessage$metadata2 !== void 0 && _lastMessage$metadata2.processing) {\n      updateMessage(lastMessage.id, {\n        metadata: {\n          ...lastMessage.metadata,\n          processing: false\n        }\n      });\n    }\n    setTyping(false);\n  }, [updateMessage, setTyping]);\n  const handleTranscription = useCallback(message => {\n    const {\n      text\n    } = message;\n    if (text && currentConversationId) {\n      // Add user message from transcription\n      addMessage({\n        role: 'user',\n        content: text,\n        conversationId: currentConversationId,\n        metadata: {\n          audioTranscription: true\n        }\n      });\n    }\n  }, [currentConversationId, addMessage]);\n  const handleConfigResponse = useCallback(message => {\n    const {\n      config_type,\n      data\n    } = message;\n    if (config_type === 'models' && data) {\n      // Convert models data to providers format\n      const providersMap = new Map();\n      data.forEach(model => {\n        if (!providersMap.has(model.provider)) {\n          providersMap.set(model.provider, {\n            name: model.provider,\n            displayName: model.provider.charAt(0).toUpperCase() + model.provider.slice(1),\n            models: [],\n            available: true\n          });\n        }\n        providersMap.get(model.provider).models.push({\n          name: model.name,\n          displayName: model.display_name,\n          provider: model.provider,\n          maxTokens: model.max_tokens,\n          temperature: model.temperature,\n          available: model.available\n        });\n      });\n      setProviders(Array.from(providersMap.values()));\n    }\n  }, [setProviders]);\n  const handleError = useCallback(message => {\n    console.error('WebSocket error:', message.message);\n    setConnectionError(message.message);\n  }, []);\n\n  // Send chat message\n  const sendChatMessage = useCallback((content, images) => {\n    if (!currentConversationId) return;\n\n    // Add user message to store\n    addMessage({\n      role: 'user',\n      content,\n      conversationId: currentConversationId,\n      metadata: {\n        model: selectedModel,\n        provider: selectedProvider\n      }\n    });\n\n    // Send to server\n    sendMessage({\n      type: 'chat',\n      content,\n      conversation_id: currentConversationId,\n      model: selectedModel,\n      provider: selectedProvider,\n      images: images || []\n    });\n  }, [currentConversationId, selectedModel, selectedProvider, addMessage, sendMessage]);\n\n  // Send audio data\n  const sendAudioData = useCallback((audioData, format = 'wav') => {\n    if (!currentConversationId) return;\n    sendMessage({\n      type: 'audio',\n      audio_data: audioData,\n      format,\n      conversation_id: currentConversationId,\n      model: selectedModel,\n      provider: selectedProvider\n    });\n  }, [currentConversationId, selectedModel, selectedProvider, sendMessage]);\n\n  // Send ping to keep connection alive\n  useEffect(() => {\n    if (!isConnected) return;\n    const pingInterval = setInterval(() => {\n      sendMessage({\n        type: 'ping'\n      });\n    }, 30000); // Ping every 30 seconds\n\n    return () => clearInterval(pingInterval);\n  }, [isConnected, sendMessage]);\n  return {\n    connect,\n    disconnect,\n    sendChatMessage,\n    sendAudioData,\n    isConnected,\n    connectionError\n  };\n};\n_s(useWebSocket, \"smwNN9EzqluhodwNpplwG4UhWM4=\", false, function () {\n  return [useChatStore];\n});", "map": {"version": 3, "names": ["useEffect", "useRef", "useCallback", "useState", "useChatStore", "useWebSocket", "_s", "socketRef", "isConnected", "setIsConnected", "connectionError", "setConnectionError", "addMessage", "updateMessage", "setTyping", "setProcessingAudio", "setProviders", "currentConversationId", "selected<PERSON><PERSON>l", "<PERSON><PERSON><PERSON><PERSON>", "connect", "_socketRef$current", "current", "readyState", "WebSocket", "OPEN", "clientId", "Date", "now", "Math", "random", "toString", "substr", "socket", "onopen", "console", "log", "send", "JSON", "stringify", "type", "config_type", "onclose", "onerror", "error", "onmessage", "event", "message", "parse", "data", "handleMessage", "disconnect", "close", "sendMessage", "_socketRef$current2", "handleChatChunk", "handleChatComplete", "handleTranscription", "status", "handleConfigResponse", "handleError", "_lastMessage$metadata", "content", "conversation_id", "conversation", "getState", "conversations", "find", "conv", "id", "lastMessage", "messages", "length", "role", "metadata", "processing", "conversationId", "model", "provider", "_lastMessage$metadata2", "text", "audioTranscription", "providersMap", "Map", "for<PERSON>ach", "has", "set", "name", "displayName", "char<PERSON>t", "toUpperCase", "slice", "models", "available", "get", "push", "display_name", "maxTokens", "max_tokens", "temperature", "Array", "from", "values", "sendChatMessage", "images", "sendAudioData", "audioData", "format", "audio_data", "pingInterval", "setInterval", "clearInterval"], "sources": ["/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/src/hooks/useWebSocket.ts"], "sourcesContent": ["import { useEffect, useRef, useCallback, useState } from 'react';\nimport { useChatStore } from '../stores/chatStore';\n\ninterface WebSocketMessage {\n  type: string;\n  [key: string]: any;\n}\n\nexport const useWebSocket = () => {\n  const socketRef = useRef<WebSocket | null>(null);\n  const [isConnected, setIsConnected] = useState(false);\n  const [connectionError, setConnectionError] = useState<string | null>(null);\n  \n  const {\n    addMessage,\n    updateMessage,\n    setTyping,\n    setProcessingAudio,\n    setProviders,\n    currentConversationId,\n    selectedModel,\n    selectedProvider,\n  } = useChatStore();\n\n  const connect = useCallback(() => {\n    if (socketRef.current?.readyState === WebSocket.OPEN) {\n      return;\n    }\n\n    const clientId = `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n\n    // Connect to WebSocket server\n    const socket = new WebSocket(`ws://localhost:8000/ws/chat/${clientId}`);\n\n    socketRef.current = socket;\n\n    socket.onopen = () => {\n      console.log('Connected to WebSocket server');\n      setIsConnected(true);\n      setConnectionError(null);\n\n      // Request available models\n      socket.send(JSON.stringify({\n        type: 'config',\n        config_type: 'get_models',\n      }));\n    };\n\n    socket.onclose = () => {\n      console.log('Disconnected from WebSocket server');\n      setIsConnected(false);\n    };\n\n    socket.onerror = (error) => {\n      console.error('WebSocket connection error:', error);\n      setConnectionError('Connection failed');\n      setIsConnected(false);\n    };\n\n    socket.onmessage = (event) => {\n      try {\n        const message: WebSocketMessage = JSON.parse(event.data);\n        handleMessage(message);\n      } catch (error) {\n        console.error('Failed to parse WebSocket message:', error);\n      }\n    };\n\n    return socket;\n  }, []);\n\n  const disconnect = useCallback(() => {\n    if (socketRef.current) {\n      socketRef.current.close();\n      socketRef.current = null;\n      setIsConnected(false);\n    }\n  }, []);\n\n  const sendMessage = useCallback((message: WebSocketMessage) => {\n    if (socketRef.current?.readyState === WebSocket.OPEN) {\n      socketRef.current.send(JSON.stringify(message));\n    } else {\n      console.error('WebSocket not connected');\n    }\n  }, []);\n\n  const handleMessage = useCallback((message: WebSocketMessage) => {\n    switch (message.type) {\n      case 'chat_chunk':\n        handleChatChunk(message);\n        break;\n      case 'chat_complete':\n        handleChatComplete(message);\n        break;\n      case 'transcription':\n        handleTranscription(message);\n        break;\n      case 'typing':\n        setTyping(message.status);\n        break;\n      case 'audio_processing':\n        setProcessingAudio(message.status);\n        break;\n      case 'config_response':\n        handleConfigResponse(message);\n        break;\n      case 'error':\n        handleError(message);\n        break;\n      case 'pong':\n        // Handle ping/pong for connection health\n        break;\n      default:\n        console.log('Unknown message type:', message.type);\n    }\n  }, [setTyping, setProcessingAudio]);\n\n  const handleChatChunk = useCallback((message: WebSocketMessage) => {\n    const { content, conversation_id } = message;\n    \n    if (!conversation_id || !currentConversationId) return;\n\n    // Find or create assistant message\n    const conversation = useChatStore.getState().conversations.find(\n      conv => conv.id === conversation_id\n    );\n    \n    if (!conversation) return;\n\n    const lastMessage = conversation.messages[conversation.messages.length - 1];\n    \n    if (lastMessage && lastMessage.role === 'assistant' && lastMessage.metadata?.processing) {\n      // Update existing message\n      updateMessage(lastMessage.id, {\n        content: lastMessage.content + content,\n      });\n    } else {\n      // Create new assistant message\n      addMessage({\n        role: 'assistant',\n        content,\n        conversationId: conversation_id,\n        metadata: {\n          processing: true,\n          model: selectedModel,\n          provider: selectedProvider,\n        },\n      });\n    }\n  }, [currentConversationId, selectedModel, selectedProvider, addMessage, updateMessage]);\n\n  const handleChatComplete = useCallback((message: WebSocketMessage) => {\n    const { conversation_id } = message;\n    \n    if (!conversation_id) return;\n\n    // Mark the last assistant message as complete\n    const conversation = useChatStore.getState().conversations.find(\n      conv => conv.id === conversation_id\n    );\n    \n    if (!conversation) return;\n\n    const lastMessage = conversation.messages[conversation.messages.length - 1];\n    \n    if (lastMessage && lastMessage.role === 'assistant' && lastMessage.metadata?.processing) {\n      updateMessage(lastMessage.id, {\n        metadata: {\n          ...lastMessage.metadata,\n          processing: false,\n        },\n      });\n    }\n    \n    setTyping(false);\n  }, [updateMessage, setTyping]);\n\n  const handleTranscription = useCallback((message: WebSocketMessage) => {\n    const { text } = message;\n    \n    if (text && currentConversationId) {\n      // Add user message from transcription\n      addMessage({\n        role: 'user',\n        content: text,\n        conversationId: currentConversationId,\n        metadata: {\n          audioTranscription: true,\n        },\n      });\n    }\n  }, [currentConversationId, addMessage]);\n\n  const handleConfigResponse = useCallback((message: WebSocketMessage) => {\n    const { config_type, data } = message;\n    \n    if (config_type === 'models' && data) {\n      // Convert models data to providers format\n      const providersMap = new Map();\n      \n      data.forEach((model: any) => {\n        if (!providersMap.has(model.provider)) {\n          providersMap.set(model.provider, {\n            name: model.provider,\n            displayName: model.provider.charAt(0).toUpperCase() + model.provider.slice(1),\n            models: [],\n            available: true,\n          });\n        }\n        \n        providersMap.get(model.provider).models.push({\n          name: model.name,\n          displayName: model.display_name,\n          provider: model.provider,\n          maxTokens: model.max_tokens,\n          temperature: model.temperature,\n          available: model.available,\n        });\n      });\n      \n      setProviders(Array.from(providersMap.values()));\n    }\n  }, [setProviders]);\n\n  const handleError = useCallback((message: WebSocketMessage) => {\n    console.error('WebSocket error:', message.message);\n    setConnectionError(message.message);\n  }, []);\n\n  // Send chat message\n  const sendChatMessage = useCallback((content: string, images?: string[]) => {\n    if (!currentConversationId) return;\n\n    // Add user message to store\n    addMessage({\n      role: 'user',\n      content,\n      conversationId: currentConversationId,\n      metadata: {\n        model: selectedModel,\n        provider: selectedProvider,\n      },\n    });\n\n    // Send to server\n    sendMessage({\n      type: 'chat',\n      content,\n      conversation_id: currentConversationId,\n      model: selectedModel,\n      provider: selectedProvider,\n      images: images || [],\n    });\n  }, [currentConversationId, selectedModel, selectedProvider, addMessage, sendMessage]);\n\n  // Send audio data\n  const sendAudioData = useCallback((audioData: string, format: string = 'wav') => {\n    if (!currentConversationId) return;\n\n    sendMessage({\n      type: 'audio',\n      audio_data: audioData,\n      format,\n      conversation_id: currentConversationId,\n      model: selectedModel,\n      provider: selectedProvider,\n    });\n  }, [currentConversationId, selectedModel, selectedProvider, sendMessage]);\n\n  // Send ping to keep connection alive\n  useEffect(() => {\n    if (!isConnected) return;\n\n    const pingInterval = setInterval(() => {\n      sendMessage({ type: 'ping' });\n    }, 30000); // Ping every 30 seconds\n\n    return () => clearInterval(pingInterval);\n  }, [isConnected, sendMessage]);\n\n  return {\n    connect,\n    disconnect,\n    sendChatMessage,\n    sendAudioData,\n    isConnected,\n    connectionError,\n  };\n};\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,MAAM,EAAEC,WAAW,EAAEC,QAAQ,QAAQ,OAAO;AAChE,SAASC,YAAY,QAAQ,qBAAqB;AAOlD,OAAO,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAMC,SAAS,GAAGN,MAAM,CAAmB,IAAI,CAAC;EAChD,MAAM,CAACO,WAAW,EAAEC,cAAc,CAAC,GAAGN,QAAQ,CAAC,KAAK,CAAC;EACrD,MAAM,CAACO,eAAe,EAAEC,kBAAkB,CAAC,GAAGR,QAAQ,CAAgB,IAAI,CAAC;EAE3E,MAAM;IACJS,UAAU;IACVC,aAAa;IACbC,SAAS;IACTC,kBAAkB;IAClBC,YAAY;IACZC,qBAAqB;IACrBC,aAAa;IACbC;EACF,CAAC,GAAGf,YAAY,CAAC,CAAC;EAElB,MAAMgB,OAAO,GAAGlB,WAAW,CAAC,MAAM;IAAA,IAAAmB,kBAAA;IAChC,IAAI,EAAAA,kBAAA,GAAAd,SAAS,CAACe,OAAO,cAAAD,kBAAA,uBAAjBA,kBAAA,CAAmBE,UAAU,MAAKC,SAAS,CAACC,IAAI,EAAE;MACpD;IACF;IAEA,MAAMC,QAAQ,GAAG,UAAUC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;;IAElF;IACA,MAAMC,MAAM,GAAG,IAAIT,SAAS,CAAC,+BAA+BE,QAAQ,EAAE,CAAC;IAEvEnB,SAAS,CAACe,OAAO,GAAGW,MAAM;IAE1BA,MAAM,CAACC,MAAM,GAAG,MAAM;MACpBC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;MAC5C3B,cAAc,CAAC,IAAI,CAAC;MACpBE,kBAAkB,CAAC,IAAI,CAAC;;MAExB;MACAsB,MAAM,CAACI,IAAI,CAACC,IAAI,CAACC,SAAS,CAAC;QACzBC,IAAI,EAAE,QAAQ;QACdC,WAAW,EAAE;MACf,CAAC,CAAC,CAAC;IACL,CAAC;IAEDR,MAAM,CAACS,OAAO,GAAG,MAAM;MACrBP,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAC;MACjD3B,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC;IAEDwB,MAAM,CAACU,OAAO,GAAIC,KAAK,IAAK;MAC1BT,OAAO,CAACS,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDjC,kBAAkB,CAAC,mBAAmB,CAAC;MACvCF,cAAc,CAAC,KAAK,CAAC;IACvB,CAAC;IAEDwB,MAAM,CAACY,SAAS,GAAIC,KAAK,IAAK;MAC5B,IAAI;QACF,MAAMC,OAAyB,GAAGT,IAAI,CAACU,KAAK,CAACF,KAAK,CAACG,IAAI,CAAC;QACxDC,aAAa,CAACH,OAAO,CAAC;MACxB,CAAC,CAAC,OAAOH,KAAK,EAAE;QACdT,OAAO,CAACS,KAAK,CAAC,oCAAoC,EAAEA,KAAK,CAAC;MAC5D;IACF,CAAC;IAED,OAAOX,MAAM;EACf,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMkB,UAAU,GAAGjD,WAAW,CAAC,MAAM;IACnC,IAAIK,SAAS,CAACe,OAAO,EAAE;MACrBf,SAAS,CAACe,OAAO,CAAC8B,KAAK,CAAC,CAAC;MACzB7C,SAAS,CAACe,OAAO,GAAG,IAAI;MACxBb,cAAc,CAAC,KAAK,CAAC;IACvB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM4C,WAAW,GAAGnD,WAAW,CAAE6C,OAAyB,IAAK;IAAA,IAAAO,mBAAA;IAC7D,IAAI,EAAAA,mBAAA,GAAA/C,SAAS,CAACe,OAAO,cAAAgC,mBAAA,uBAAjBA,mBAAA,CAAmB/B,UAAU,MAAKC,SAAS,CAACC,IAAI,EAAE;MACpDlB,SAAS,CAACe,OAAO,CAACe,IAAI,CAACC,IAAI,CAACC,SAAS,CAACQ,OAAO,CAAC,CAAC;IACjD,CAAC,MAAM;MACLZ,OAAO,CAACS,KAAK,CAAC,yBAAyB,CAAC;IAC1C;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,aAAa,GAAGhD,WAAW,CAAE6C,OAAyB,IAAK;IAC/D,QAAQA,OAAO,CAACP,IAAI;MAClB,KAAK,YAAY;QACfe,eAAe,CAACR,OAAO,CAAC;QACxB;MACF,KAAK,eAAe;QAClBS,kBAAkB,CAACT,OAAO,CAAC;QAC3B;MACF,KAAK,eAAe;QAClBU,mBAAmB,CAACV,OAAO,CAAC;QAC5B;MACF,KAAK,QAAQ;QACXjC,SAAS,CAACiC,OAAO,CAACW,MAAM,CAAC;QACzB;MACF,KAAK,kBAAkB;QACrB3C,kBAAkB,CAACgC,OAAO,CAACW,MAAM,CAAC;QAClC;MACF,KAAK,iBAAiB;QACpBC,oBAAoB,CAACZ,OAAO,CAAC;QAC7B;MACF,KAAK,OAAO;QACVa,WAAW,CAACb,OAAO,CAAC;QACpB;MACF,KAAK,MAAM;QACT;QACA;MACF;QACEZ,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEW,OAAO,CAACP,IAAI,CAAC;IACtD;EACF,CAAC,EAAE,CAAC1B,SAAS,EAAEC,kBAAkB,CAAC,CAAC;EAEnC,MAAMwC,eAAe,GAAGrD,WAAW,CAAE6C,OAAyB,IAAK;IAAA,IAAAc,qBAAA;IACjE,MAAM;MAAEC,OAAO;MAAEC;IAAgB,CAAC,GAAGhB,OAAO;IAE5C,IAAI,CAACgB,eAAe,IAAI,CAAC9C,qBAAqB,EAAE;;IAEhD;IACA,MAAM+C,YAAY,GAAG5D,YAAY,CAAC6D,QAAQ,CAAC,CAAC,CAACC,aAAa,CAACC,IAAI,CAC7DC,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKN,eACtB,CAAC;IAED,IAAI,CAACC,YAAY,EAAE;IAEnB,MAAMM,WAAW,GAAGN,YAAY,CAACO,QAAQ,CAACP,YAAY,CAACO,QAAQ,CAACC,MAAM,GAAG,CAAC,CAAC;IAE3E,IAAIF,WAAW,IAAIA,WAAW,CAACG,IAAI,KAAK,WAAW,KAAAZ,qBAAA,GAAIS,WAAW,CAACI,QAAQ,cAAAb,qBAAA,eAApBA,qBAAA,CAAsBc,UAAU,EAAE;MACvF;MACA9D,aAAa,CAACyD,WAAW,CAACD,EAAE,EAAE;QAC5BP,OAAO,EAAEQ,WAAW,CAACR,OAAO,GAAGA;MACjC,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACAlD,UAAU,CAAC;QACT6D,IAAI,EAAE,WAAW;QACjBX,OAAO;QACPc,cAAc,EAAEb,eAAe;QAC/BW,QAAQ,EAAE;UACRC,UAAU,EAAE,IAAI;UAChBE,KAAK,EAAE3D,aAAa;UACpB4D,QAAQ,EAAE3D;QACZ;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACF,qBAAqB,EAAEC,aAAa,EAAEC,gBAAgB,EAAEP,UAAU,EAAEC,aAAa,CAAC,CAAC;EAEvF,MAAM2C,kBAAkB,GAAGtD,WAAW,CAAE6C,OAAyB,IAAK;IAAA,IAAAgC,sBAAA;IACpE,MAAM;MAAEhB;IAAgB,CAAC,GAAGhB,OAAO;IAEnC,IAAI,CAACgB,eAAe,EAAE;;IAEtB;IACA,MAAMC,YAAY,GAAG5D,YAAY,CAAC6D,QAAQ,CAAC,CAAC,CAACC,aAAa,CAACC,IAAI,CAC7DC,IAAI,IAAIA,IAAI,CAACC,EAAE,KAAKN,eACtB,CAAC;IAED,IAAI,CAACC,YAAY,EAAE;IAEnB,MAAMM,WAAW,GAAGN,YAAY,CAACO,QAAQ,CAACP,YAAY,CAACO,QAAQ,CAACC,MAAM,GAAG,CAAC,CAAC;IAE3E,IAAIF,WAAW,IAAIA,WAAW,CAACG,IAAI,KAAK,WAAW,KAAAM,sBAAA,GAAIT,WAAW,CAACI,QAAQ,cAAAK,sBAAA,eAApBA,sBAAA,CAAsBJ,UAAU,EAAE;MACvF9D,aAAa,CAACyD,WAAW,CAACD,EAAE,EAAE;QAC5BK,QAAQ,EAAE;UACR,GAAGJ,WAAW,CAACI,QAAQ;UACvBC,UAAU,EAAE;QACd;MACF,CAAC,CAAC;IACJ;IAEA7D,SAAS,CAAC,KAAK,CAAC;EAClB,CAAC,EAAE,CAACD,aAAa,EAAEC,SAAS,CAAC,CAAC;EAE9B,MAAM2C,mBAAmB,GAAGvD,WAAW,CAAE6C,OAAyB,IAAK;IACrE,MAAM;MAAEiC;IAAK,CAAC,GAAGjC,OAAO;IAExB,IAAIiC,IAAI,IAAI/D,qBAAqB,EAAE;MACjC;MACAL,UAAU,CAAC;QACT6D,IAAI,EAAE,MAAM;QACZX,OAAO,EAAEkB,IAAI;QACbJ,cAAc,EAAE3D,qBAAqB;QACrCyD,QAAQ,EAAE;UACRO,kBAAkB,EAAE;QACtB;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAChE,qBAAqB,EAAEL,UAAU,CAAC,CAAC;EAEvC,MAAM+C,oBAAoB,GAAGzD,WAAW,CAAE6C,OAAyB,IAAK;IACtE,MAAM;MAAEN,WAAW;MAAEQ;IAAK,CAAC,GAAGF,OAAO;IAErC,IAAIN,WAAW,KAAK,QAAQ,IAAIQ,IAAI,EAAE;MACpC;MACA,MAAMiC,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;MAE9BlC,IAAI,CAACmC,OAAO,CAAEP,KAAU,IAAK;QAC3B,IAAI,CAACK,YAAY,CAACG,GAAG,CAACR,KAAK,CAACC,QAAQ,CAAC,EAAE;UACrCI,YAAY,CAACI,GAAG,CAACT,KAAK,CAACC,QAAQ,EAAE;YAC/BS,IAAI,EAAEV,KAAK,CAACC,QAAQ;YACpBU,WAAW,EAAEX,KAAK,CAACC,QAAQ,CAACW,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGb,KAAK,CAACC,QAAQ,CAACa,KAAK,CAAC,CAAC,CAAC;YAC7EC,MAAM,EAAE,EAAE;YACVC,SAAS,EAAE;UACb,CAAC,CAAC;QACJ;QAEAX,YAAY,CAACY,GAAG,CAACjB,KAAK,CAACC,QAAQ,CAAC,CAACc,MAAM,CAACG,IAAI,CAAC;UAC3CR,IAAI,EAAEV,KAAK,CAACU,IAAI;UAChBC,WAAW,EAAEX,KAAK,CAACmB,YAAY;UAC/BlB,QAAQ,EAAED,KAAK,CAACC,QAAQ;UACxBmB,SAAS,EAAEpB,KAAK,CAACqB,UAAU;UAC3BC,WAAW,EAAEtB,KAAK,CAACsB,WAAW;UAC9BN,SAAS,EAAEhB,KAAK,CAACgB;QACnB,CAAC,CAAC;MACJ,CAAC,CAAC;MAEF7E,YAAY,CAACoF,KAAK,CAACC,IAAI,CAACnB,YAAY,CAACoB,MAAM,CAAC,CAAC,CAAC,CAAC;IACjD;EACF,CAAC,EAAE,CAACtF,YAAY,CAAC,CAAC;EAElB,MAAM4C,WAAW,GAAG1D,WAAW,CAAE6C,OAAyB,IAAK;IAC7DZ,OAAO,CAACS,KAAK,CAAC,kBAAkB,EAAEG,OAAO,CAACA,OAAO,CAAC;IAClDpC,kBAAkB,CAACoC,OAAO,CAACA,OAAO,CAAC;EACrC,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMwD,eAAe,GAAGrG,WAAW,CAAC,CAAC4D,OAAe,EAAE0C,MAAiB,KAAK;IAC1E,IAAI,CAACvF,qBAAqB,EAAE;;IAE5B;IACAL,UAAU,CAAC;MACT6D,IAAI,EAAE,MAAM;MACZX,OAAO;MACPc,cAAc,EAAE3D,qBAAqB;MACrCyD,QAAQ,EAAE;QACRG,KAAK,EAAE3D,aAAa;QACpB4D,QAAQ,EAAE3D;MACZ;IACF,CAAC,CAAC;;IAEF;IACAkC,WAAW,CAAC;MACVb,IAAI,EAAE,MAAM;MACZsB,OAAO;MACPC,eAAe,EAAE9C,qBAAqB;MACtC4D,KAAK,EAAE3D,aAAa;MACpB4D,QAAQ,EAAE3D,gBAAgB;MAC1BqF,MAAM,EAAEA,MAAM,IAAI;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACvF,qBAAqB,EAAEC,aAAa,EAAEC,gBAAgB,EAAEP,UAAU,EAAEyC,WAAW,CAAC,CAAC;;EAErF;EACA,MAAMoD,aAAa,GAAGvG,WAAW,CAAC,CAACwG,SAAiB,EAAEC,MAAc,GAAG,KAAK,KAAK;IAC/E,IAAI,CAAC1F,qBAAqB,EAAE;IAE5BoC,WAAW,CAAC;MACVb,IAAI,EAAE,OAAO;MACboE,UAAU,EAAEF,SAAS;MACrBC,MAAM;MACN5C,eAAe,EAAE9C,qBAAqB;MACtC4D,KAAK,EAAE3D,aAAa;MACpB4D,QAAQ,EAAE3D;IACZ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACF,qBAAqB,EAAEC,aAAa,EAAEC,gBAAgB,EAAEkC,WAAW,CAAC,CAAC;;EAEzE;EACArD,SAAS,CAAC,MAAM;IACd,IAAI,CAACQ,WAAW,EAAE;IAElB,MAAMqG,YAAY,GAAGC,WAAW,CAAC,MAAM;MACrCzD,WAAW,CAAC;QAAEb,IAAI,EAAE;MAAO,CAAC,CAAC;IAC/B,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;IAEX,OAAO,MAAMuE,aAAa,CAACF,YAAY,CAAC;EAC1C,CAAC,EAAE,CAACrG,WAAW,EAAE6C,WAAW,CAAC,CAAC;EAE9B,OAAO;IACLjC,OAAO;IACP+B,UAAU;IACVoD,eAAe;IACfE,aAAa;IACbjG,WAAW;IACXE;EACF,CAAC;AACH,CAAC;AAACJ,EAAA,CAzRWD,YAAY;EAAA,QAcnBD,YAAY;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}