[{"/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/src/index.tsx": "1", "/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/src/App.tsx": "2", "/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/src/stores/chatStore.ts": "3", "/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/src/hooks/useWebSocket.ts": "4", "/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/src/components/Chat/ChatInterface.tsx": "5", "/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/src/components/Layout/Sidebar.tsx": "6", "/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/src/components/Layout/Header.tsx": "7", "/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/src/components/Settings/Settings.tsx": "8", "/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/src/components/Chat/MessageList.tsx": "9", "/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/src/components/Chat/ChatInput.tsx": "10", "/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/src/components/Chat/ModelSelector.tsx": "11"}, {"size": 1477, "mtime": 1751638822227, "results": "12", "hashOfConfig": "13"}, {"size": 2766, "mtime": 1751615627307, "results": "14", "hashOfConfig": "13"}, {"size": 10240, "mtime": 1751615592079, "results": "15", "hashOfConfig": "13"}, {"size": 8096, "mtime": 1751639119461, "results": "16", "hashOfConfig": "13"}, {"size": 4641, "mtime": 1751614550910, "results": "17", "hashOfConfig": "13"}, {"size": 7327, "mtime": 1751614529273, "results": "18", "hashOfConfig": "13"}, {"size": 2826, "mtime": 1751614498594, "results": "19", "hashOfConfig": "13"}, {"size": 7387, "mtime": 1751614649434, "results": "20", "hashOfConfig": "13"}, {"size": 4442, "mtime": 1751614572722, "results": "21", "hashOfConfig": "13"}, {"size": 6584, "mtime": 1751614601457, "results": "22", "hashOfConfig": "13"}, {"size": 3146, "mtime": 1751614620211, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "hqz2vp", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/src/index.tsx", [], [], "/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/src/App.tsx", ["57"], [], "/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/src/stores/chatStore.ts", [], [], "/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/src/hooks/useWebSocket.ts", ["58", "59"], [], "/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/src/components/Chat/ChatInterface.tsx", [], [], "/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/src/components/Layout/Sidebar.tsx", ["60", "61"], [], "/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/src/components/Layout/Header.tsx", [], [], "/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/src/components/Settings/Settings.tsx", [], [], "/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/src/components/Chat/MessageList.tsx", [], [], "/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/src/components/Chat/ChatInput.tsx", ["62"], [], "/Users/<USER>/Downloads/test/Open-LLM-VTuber/newnew/frontend/src/components/Chat/ModelSelector.tsx", [], [], {"ruleId": "63", "severity": 1, "message": "64", "line": 3, "column": 15, "nodeType": "65", "messageId": "66", "endLine": 3, "endColumn": 24}, {"ruleId": "67", "severity": 1, "message": "68", "line": 70, "column": 6, "nodeType": "69", "endLine": 70, "endColumn": 8, "suggestions": "70"}, {"ruleId": "67", "severity": 1, "message": "71", "line": 117, "column": 6, "nodeType": "69", "endLine": 117, "endColumn": 37, "suggestions": "72"}, {"ruleId": "63", "severity": 1, "message": "73", "line": 12, "column": 3, "nodeType": "65", "messageId": "66", "endLine": 12, "endColumn": 10}, {"ruleId": "63", "severity": 1, "message": "74", "line": 20, "column": 11, "nodeType": "65", "messageId": "66", "endLine": 20, "endColumn": 19}, {"ruleId": "63", "severity": 1, "message": "75", "line": 6, "column": 3, "nodeType": "65", "messageId": "66", "endLine": 6, "endColumn": 9}, "@typescript-eslint/no-unused-vars", "'Container' is defined but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'handleMessage'. Either include it or remove the dependency array.", "ArrayExpression", ["76"], "React Hook useCallback has missing dependencies: 'handleChatChunk', 'handleChatComplete', 'handleConfigResponse', 'handleError', and 'handleTranscription'. Either include them or remove the dependency array.", ["77"], "'Divider' is defined but never used.", "'EditIcon' is defined but never used.", "'Button' is defined but never used.", {"desc": "78", "fix": "79"}, {"desc": "80", "fix": "81"}, "Update the dependencies array to be: [handleMessage]", {"range": "82", "text": "83"}, "Update the dependencies array to be: [handleChatChunk, handleChatComplete, handleTranscription, setTyping, setProcessingAudio, handleConfigResponse, handleError]", {"range": "84", "text": "85"}, [1829, 1831], "[handleMessage]", [3088, 3119], "[handleChatChunk, handleChatComplete, handleTranscription, setTyping, setProcessingAudio, handleConfigResponse, handleError]"]