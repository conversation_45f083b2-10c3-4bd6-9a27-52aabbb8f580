{"version": 3, "names": ["babel", "require", "semver", "ESLINT_VERSION", "get<PERSON><PERSON>erPlugins", "babelOptions", "_babelOptions$parserO", "_babelOptions$parserO2", "babelParserPlugins", "parserOpts", "plugins", "estreeOptions", "classFeatures", "plugin", "Array", "isArray", "Object", "assign", "normalizeParserOptions", "options", "version", "_options$allowImportE", "_options$ecmaFeatures2", "_options$ecmaFeatures3", "sourceType", "satisfies", "_options$ecmaFeatures", "ecmaFeatures", "globalReturn", "filename", "filePath", "allowImportExportEverywhere", "allowSuperOutsideMethod", "allowReturnOutsideFunction", "attachComment", "ranges", "tokens", "caller", "name", "validateResolvedConfig", "config", "parseOptions", "requireConfigFile", "hasFilesystemConfig", "error", "includes", "Error", "getDefaultParserOptions", "babelrc", "configFile", "browserslistConfigFile", "ignore", "only", "normalizeBabelParseConfig", "_x", "_normalizeBabelParseConfig", "apply", "arguments", "_asyncToGenerator", "loadPartialConfigAsync", "normalizeBabelParseConfigSync", "loadPartialConfigSync"], "sources": ["../../src/worker/configuration.cts"], "sourcesContent": ["import babel = require(\"./babel-core.cts\");\nimport semver = require(\"semver\");\nimport ESLINT_VERSION = require(\"../utils/eslint-version.cts\");\nimport type { InputOptions } from \"@babel/core\";\nimport type { Options } from \"../types.cts\";\nimport type { PartialConfig } from \"../../../../packages/babel-core/src/config\";\n\n/**\n * Merge user supplied estree plugin options to default estree plugin options\n *\n * @returns {Array} Merged parser plugin descriptors\n */\nfunction getParserPlugins(\n  babelOptions: InputOptions,\n): InputOptions[\"parserOpts\"][\"plugins\"] {\n  const babelParserPlugins = babelOptions.parserOpts?.plugins ?? [];\n  const estreeOptions = { classFeatures: ESLINT_VERSION >= 8 };\n  for (const plugin of babelParserPlugins) {\n    if (Array.isArray(plugin) && plugin[0] === \"estree\") {\n      Object.assign(estreeOptions, plugin[1]);\n      break;\n    }\n  }\n  // estree must be the first parser plugin to work with other parser plugins\n  return [[\"estree\", estreeOptions], ...babelParserPlugins];\n}\n\nfunction normalizeParserOptions(\n  options: Options,\n  version: string,\n): InputOptions & {\n  showIgnoredFiles?: boolean;\n} {\n  // Babel <= 7.28.0 does not support `sourceType: \"commonjs\"`.\n  if (\n    !process.env.BABEL_8_BREAKING &&\n    options.sourceType === \"commonjs\" &&\n    !semver.satisfies(version, REQUIRED_VERSION(\">=7.28.0\"))\n  ) {\n    options.sourceType = \"script\";\n    options.ecmaFeatures = {\n      ...(options.ecmaFeatures ?? {}),\n      globalReturn: true,\n    };\n  }\n  return {\n    sourceType: options.sourceType,\n    filename: options.filePath,\n    ...options.babelOptions,\n    parserOpts: {\n      ...(process.env.BABEL_8_BREAKING\n        ? {}\n        : {\n            allowImportExportEverywhere:\n              options.allowImportExportEverywhere ?? false,\n            allowSuperOutsideMethod: true,\n          }),\n      ...(options.sourceType !== \"commonjs\"\n        ? {\n            allowReturnOutsideFunction:\n              options.ecmaFeatures?.globalReturn ??\n              (process.env.BABEL_8_BREAKING ? false : true),\n          }\n        : {}),\n      ...options.babelOptions.parserOpts,\n      plugins: getParserPlugins(options.babelOptions),\n      // skip comment attaching for parsing performance\n      attachComment: false,\n      ranges: true,\n      tokens: true,\n    },\n    caller: {\n      name: \"@babel/eslint-parser\",\n      ...options.babelOptions.caller,\n    },\n  };\n}\n\nfunction validateResolvedConfig(\n  config: PartialConfig,\n  options: Options,\n  parseOptions: InputOptions,\n) {\n  if (config !== null) {\n    if (options.requireConfigFile !== false) {\n      if (!config.hasFilesystemConfig()) {\n        let error = `No Babel config file detected for ${config.options.filename}. Either disable config file checking with requireConfigFile: false, or configure Babel so that it can find the config files.`;\n\n        if (config.options.filename.includes(\"node_modules\")) {\n          error += `\\nIf you have a .babelrc.js file or use package.json#babel, keep in mind that it's not used when parsing dependencies. If you want your config to be applied to your whole app, consider using babel.config.js or babel.config.json instead.`;\n        }\n\n        throw new Error(error);\n      }\n    }\n    if (config.options) return config.options;\n  }\n\n  return getDefaultParserOptions(parseOptions);\n}\n\nfunction getDefaultParserOptions(options: InputOptions): InputOptions {\n  return {\n    plugins: [],\n    ...options,\n    babelrc: false,\n    configFile: false,\n    browserslistConfigFile: false,\n    ignore: null,\n    only: null,\n  };\n}\n\nexport async function normalizeBabelParseConfig(\n  options: Options,\n): Promise<InputOptions> {\n  const parseOptions = normalizeParserOptions(options, babel.version);\n  const config = await babel.loadPartialConfigAsync(parseOptions);\n  return validateResolvedConfig(config, options, parseOptions);\n}\n\nexport function normalizeBabelParseConfigSync(options: Options): InputOptions {\n  const parseOptions = normalizeParserOptions(options, babel.version);\n  const config = babel.loadPartialConfigSync(parseOptions);\n  return validateResolvedConfig(config, options, parseOptions);\n}\n"], "mappings": ";;;;;;;;;MAAOA,KAAK,GAAAC,OAAA,CAAW,kBAAkB;AAAA,MAClCC,MAAM,GAAAD,OAAA,CAAW,QAAQ;AAAA,MACzBE,cAAc,GAAAF,OAAA,CAAW,6BAA6B;AAU7D,SAASG,gBAAgBA,CACvBC,YAA0B,EACa;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EACvC,MAAMC,kBAAkB,IAAAF,qBAAA,IAAAC,sBAAA,GAAGF,YAAY,CAACI,UAAU,qBAAvBF,sBAAA,CAAyBG,OAAO,YAAAJ,qBAAA,GAAI,EAAE;EACjE,MAAMK,aAAa,GAAG;IAAEC,aAAa,EAAET,cAAc,IAAI;EAAE,CAAC;EAC5D,KAAK,MAAMU,MAAM,IAAIL,kBAAkB,EAAE;IACvC,IAAIM,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,IAAIA,MAAM,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;MACnDG,MAAM,CAACC,MAAM,CAACN,aAAa,EAAEE,MAAM,CAAC,CAAC,CAAC,CAAC;MACvC;IACF;EACF;EAEA,OAAO,CAAC,CAAC,QAAQ,EAAEF,aAAa,CAAC,EAAE,GAAGH,kBAAkB,CAAC;AAC3D;AAEA,SAASU,sBAAsBA,CAC7BC,OAAgB,EAChBC,OAAe,EAGf;EAAA,IAAAC,qBAAA,EAAAC,sBAAA,EAAAC,sBAAA;EAEA,IAEEJ,OAAO,CAACK,UAAU,KAAK,UAAU,IACjC,CAACtB,MAAM,CAACuB,SAAS,CAACL,OAAO,EAAmB,UAAW,CAAC,EACxD;IAAA,IAAAM,qBAAA;IACAP,OAAO,CAACK,UAAU,GAAG,QAAQ;IAC7BL,OAAO,CAACQ,YAAY,GAAAX,MAAA,CAAAC,MAAA,MAAAS,qBAAA,GACdP,OAAO,CAACQ,YAAY,YAAAD,qBAAA,GAAI,CAAC,CAAC;MAC9BE,YAAY,EAAE;IAAI,EACnB;EACH;EACA,OAAAZ,MAAA,CAAAC,MAAA;IACEO,UAAU,EAAEL,OAAO,CAACK,UAAU;IAC9BK,QAAQ,EAAEV,OAAO,CAACW;EAAQ,GACvBX,OAAO,CAACd,YAAY;IACvBI,UAAU,EAAAO,MAAA,CAAAC,MAAA,KAGJ;MACEc,2BAA2B,GAAAV,qBAAA,GACzBF,OAAO,CAACY,2BAA2B,YAAAV,qBAAA,GAAI,KAAK;MAC9CW,uBAAuB,EAAE;IAC3B,CAAC,EACDb,OAAO,CAACK,UAAU,KAAK,UAAU,GACjC;MACES,0BAA0B,GAAAX,sBAAA,IAAAC,sBAAA,GACxBJ,OAAO,CAACQ,YAAY,qBAApBJ,sBAAA,CAAsBK,YAAY,YAAAN,sBAAA,GACM;IAC5C,CAAC,GACD,CAAC,CAAC,EACHH,OAAO,CAACd,YAAY,CAACI,UAAU;MAClCC,OAAO,EAAEN,gBAAgB,CAACe,OAAO,CAACd,YAAY,CAAC;MAE/C6B,aAAa,EAAE,KAAK;MACpBC,MAAM,EAAE,IAAI;MACZC,MAAM,EAAE;IAAI,EACb;IACDC,MAAM,EAAArB,MAAA,CAAAC,MAAA;MACJqB,IAAI,EAAE;IAAsB,GACzBnB,OAAO,CAACd,YAAY,CAACgC,MAAM;EAC/B;AAEL;AAEA,SAASE,sBAAsBA,CAC7BC,MAAqB,EACrBrB,OAAgB,EAChBsB,YAA0B,EAC1B;EACA,IAAID,MAAM,KAAK,IAAI,EAAE;IACnB,IAAIrB,OAAO,CAACuB,iBAAiB,KAAK,KAAK,EAAE;MACvC,IAAI,CAACF,MAAM,CAACG,mBAAmB,CAAC,CAAC,EAAE;QACjC,IAAIC,KAAK,GAAG,qCAAqCJ,MAAM,CAACrB,OAAO,CAACU,QAAQ,+HAA+H;QAEvM,IAAIW,MAAM,CAACrB,OAAO,CAACU,QAAQ,CAACgB,QAAQ,CAAC,cAAc,CAAC,EAAE;UACpDD,KAAK,IAAI,8OAA8O;QACzP;QAEA,MAAM,IAAIE,KAAK,CAACF,KAAK,CAAC;MACxB;IACF;IACA,IAAIJ,MAAM,CAACrB,OAAO,EAAE,OAAOqB,MAAM,CAACrB,OAAO;EAC3C;EAEA,OAAO4B,uBAAuB,CAACN,YAAY,CAAC;AAC9C;AAEA,SAASM,uBAAuBA,CAAC5B,OAAqB,EAAgB;EACpE,OAAAH,MAAA,CAAAC,MAAA;IACEP,OAAO,EAAE;EAAE,GACRS,OAAO;IACV6B,OAAO,EAAE,KAAK;IACdC,UAAU,EAAE,KAAK;IACjBC,sBAAsB,EAAE,KAAK;IAC7BC,MAAM,EAAE,IAAI;IACZC,IAAI,EAAE;EAAI;AAEd;AAAC,SAEqBC,yBAAyBA,CAAAC,EAAA;EAAA,OAAAC,0BAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,2BAAA;EAAAA,0BAAA,GAAAG,iBAAA,CAAxC,WACLvC,OAAgB,EACO;IACvB,MAAMsB,YAAY,GAAGvB,sBAAsB,CAACC,OAAO,EAAEnB,KAAK,CAACoB,OAAO,CAAC;IACnE,MAAMoB,MAAM,SAASxC,KAAK,CAAC2D,sBAAsB,CAAClB,YAAY,CAAC;IAC/D,OAAOF,sBAAsB,CAACC,MAAM,EAAErB,OAAO,EAAEsB,YAAY,CAAC;EAC9D,CAAC;EAAA,OAAAc,0BAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAEM,SAASG,6BAA6BA,CAACzC,OAAgB,EAAgB;EAC5E,MAAMsB,YAAY,GAAGvB,sBAAsB,CAACC,OAAO,EAAEnB,KAAK,CAACoB,OAAO,CAAC;EACnE,MAAMoB,MAAM,GAAGxC,KAAK,CAAC6D,qBAAqB,CAACpB,YAAY,CAAC;EACxD,OAAOF,sBAAsB,CAACC,MAAM,EAAErB,OAAO,EAAEsB,YAAY,CAAC;AAC9D", "ignoreList": []}