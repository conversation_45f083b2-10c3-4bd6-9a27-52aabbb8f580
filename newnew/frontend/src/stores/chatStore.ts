import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  conversationId: string;
  metadata?: {
    model?: string;
    provider?: string;
    audioUrl?: string;
    imageUrl?: string;
    processing?: boolean;
  };
}

export interface Conversation {
  id: string;
  title: string;
  createdAt: Date;
  updatedAt: Date;
  messages: Message[];
  metadata?: {
    model?: string;
    provider?: string;
  };
}

export interface Model {
  name: string;
  displayName: string;
  provider: string;
  maxTokens: number;
  temperature: number;
  available: boolean;
}

export interface Provider {
  name: string;
  displayName: string;
  models: Model[];
  available: boolean;
}

interface ChatState {
  // Conversations
  conversations: Conversation[];
  currentConversationId: string | null;
  
  // Models and providers
  providers: Provider[];
  selectedModel: string;
  selectedProvider: string;
  
  // UI state
  isLoading: boolean;
  isTyping: boolean;
  isRecording: boolean;
  isProcessingAudio: boolean;
  sidebarOpen: boolean;
  
  // Settings
  settings: {
    audioEnabled: boolean;
    autoPlayAudio: boolean;
    voiceId: string;
    temperature: number;
    maxTokens: number;
  };
  
  // Actions
  initializeApp: () => void;
  
  // Conversation actions
  createNewConversation: () => void;
  selectConversation: (id: string) => void;
  deleteConversation: (id: string) => void;
  updateConversationTitle: (id: string, title: string) => void;
  
  // Message actions
  addMessage: (message: Omit<Message, 'id' | 'timestamp'>) => void;
  updateMessage: (id: string, updates: Partial<Message>) => void;
  deleteMessage: (id: string) => void;
  
  // Model actions
  setProviders: (providers: Provider[]) => void;
  selectModel: (provider: string, model: string) => void;
  
  // UI actions
  setLoading: (loading: boolean) => void;
  setTyping: (typing: boolean) => void;
  setRecording: (recording: boolean) => void;
  setProcessingAudio: (processing: boolean) => void;
  setSidebarOpen: (open: boolean) => void;
  
  // Settings actions
  updateSettings: (settings: Partial<ChatState['settings']>) => void;
}

export const useChatStore = create<ChatState>()(
  devtools(
    (set, get) => ({
      // Initial state
      conversations: [],
      currentConversationId: null,
      providers: [],
      selectedModel: '',
      selectedProvider: '',
      isLoading: false,
      isTyping: false,
      isRecording: false,
      isProcessingAudio: false,
      sidebarOpen: true,
      settings: {
        audioEnabled: true,
        autoPlayAudio: true,
        voiceId: 'en-US-AriaNeural',
        temperature: 0.7,
        maxTokens: 4096,
      },
      
      // Actions
      initializeApp: () => {
        // Load data from localStorage
        const savedConversations = localStorage.getItem('chatbot-conversations');
        const savedSettings = localStorage.getItem('chatbot-settings');
        const savedSelectedModel = localStorage.getItem('chatbot-selected-model');
        const savedSelectedProvider = localStorage.getItem('chatbot-selected-provider');
        
        if (savedConversations) {
          try {
            const conversations = JSON.parse(savedConversations).map((conv: any) => ({
              ...conv,
              createdAt: new Date(conv.createdAt),
              updatedAt: new Date(conv.updatedAt),
              messages: conv.messages.map((msg: any) => ({
                ...msg,
                timestamp: new Date(msg.timestamp),
              })),
            }));
            set({ conversations });
          } catch (error) {
            console.error('Failed to load conversations:', error);
          }
        }
        
        if (savedSettings) {
          try {
            const settings = JSON.parse(savedSettings);
            set({ settings: { ...get().settings, ...settings } });
          } catch (error) {
            console.error('Failed to load settings:', error);
          }
        }
        
        if (savedSelectedModel && savedSelectedProvider) {
          set({
            selectedModel: savedSelectedModel,
            selectedProvider: savedSelectedProvider,
          });
        }
        
        // Create initial conversation if none exist
        const { conversations } = get();
        if (conversations.length === 0) {
          get().createNewConversation();
        }
      },
      
      createNewConversation: () => {
        const id = `conv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const newConversation: Conversation = {
          id,
          title: 'New Conversation',
          createdAt: new Date(),
          updatedAt: new Date(),
          messages: [],
          metadata: {
            model: get().selectedModel,
            provider: get().selectedProvider,
          },
        };
        
        set((state) => ({
          conversations: [newConversation, ...state.conversations],
          currentConversationId: id,
        }));
        
        // Save to localStorage
        const { conversations } = get();
        localStorage.setItem('chatbot-conversations', JSON.stringify(conversations));
      },
      
      selectConversation: (id: string) => {
        set({ currentConversationId: id });
      },
      
      deleteConversation: (id: string) => {
        set((state) => {
          const newConversations = state.conversations.filter(conv => conv.id !== id);
          const newCurrentId = state.currentConversationId === id 
            ? (newConversations[0]?.id || null)
            : state.currentConversationId;
          
          return {
            conversations: newConversations,
            currentConversationId: newCurrentId,
          };
        });
        
        // Save to localStorage
        const { conversations } = get();
        localStorage.setItem('chatbot-conversations', JSON.stringify(conversations));
      },
      
      updateConversationTitle: (id: string, title: string) => {
        set((state) => ({
          conversations: state.conversations.map(conv =>
            conv.id === id
              ? { ...conv, title, updatedAt: new Date() }
              : conv
          ),
        }));
        
        // Save to localStorage
        const { conversations } = get();
        localStorage.setItem('chatbot-conversations', JSON.stringify(conversations));
      },
      
      addMessage: (message) => {
        const id = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        const newMessage: Message = {
          ...message,
          id,
          timestamp: new Date(),
        };
        
        set((state) => ({
          conversations: state.conversations.map(conv =>
            conv.id === message.conversationId
              ? {
                  ...conv,
                  messages: [...conv.messages, newMessage],
                  updatedAt: new Date(),
                  title: conv.messages.length === 0 && message.role === 'user'
                    ? message.content.slice(0, 50) + (message.content.length > 50 ? '...' : '')
                    : conv.title,
                }
              : conv
          ),
        }));
        
        // Save to localStorage
        const { conversations } = get();
        localStorage.setItem('chatbot-conversations', JSON.stringify(conversations));
      },
      
      updateMessage: (id: string, updates: Partial<Message>) => {
        set((state) => ({
          conversations: state.conversations.map(conv => ({
            ...conv,
            messages: conv.messages.map(msg =>
              msg.id === id ? { ...msg, ...updates } : msg
            ),
          })),
        }));
        
        // Save to localStorage
        const { conversations } = get();
        localStorage.setItem('chatbot-conversations', JSON.stringify(conversations));
      },
      
      deleteMessage: (id: string) => {
        set((state) => ({
          conversations: state.conversations.map(conv => ({
            ...conv,
            messages: conv.messages.filter(msg => msg.id !== id),
          })),
        }));
        
        // Save to localStorage
        const { conversations } = get();
        localStorage.setItem('chatbot-conversations', JSON.stringify(conversations));
      },
      
      setProviders: (providers: Provider[]) => {
        set({ providers });
        
        // Auto-select first available model if none selected
        const { selectedModel, selectedProvider } = get();
        if (!selectedModel || !selectedProvider) {
          const firstProvider = providers.find(p => p.available);
          const firstModel = firstProvider?.models.find(m => m.available);
          
          if (firstProvider && firstModel) {
            set({
              selectedProvider: firstProvider.name,
              selectedModel: firstModel.name,
            });
            
            localStorage.setItem('chatbot-selected-provider', firstProvider.name);
            localStorage.setItem('chatbot-selected-model', firstModel.name);
          }
        }
      },
      
      selectModel: (provider: string, model: string) => {
        set({ selectedProvider: provider, selectedModel: model });
        localStorage.setItem('chatbot-selected-provider', provider);
        localStorage.setItem('chatbot-selected-model', model);
      },
      
      setLoading: (loading: boolean) => set({ isLoading: loading }),
      setTyping: (typing: boolean) => set({ isTyping: typing }),
      setRecording: (recording: boolean) => set({ isRecording: recording }),
      setProcessingAudio: (processing: boolean) => set({ isProcessingAudio: processing }),
      setSidebarOpen: (open: boolean) => set({ sidebarOpen: open }),
      
      updateSettings: (newSettings) => {
        set((state) => ({
          settings: { ...state.settings, ...newSettings },
        }));
        
        const { settings } = get();
        localStorage.setItem('chatbot-settings', JSON.stringify(settings));
      },
    }),
    {
      name: 'chatbot-store',
    }
  )
);
