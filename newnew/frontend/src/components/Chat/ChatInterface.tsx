import React, { useEffect, useRef } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
} from '@mui/material';
import { useChatStore } from '../../stores/chatStore';
import MessageList from './MessageList';
import ChatInput from './ChatInput';
import ModelSelector from './ModelSelector';

const ChatInterface: React.FC = () => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const {
    conversations,
    currentConversationId,
    createNewConversation,
  } = useChatStore();

  // Get current conversation
  const currentConversation = conversations.find(
    conv => conv.id === currentConversationId
  );

  // Create initial conversation if none exists
  useEffect(() => {
    if (!currentConversationId && conversations.length === 0) {
      createNewConversation();
    }
  }, [currentConversationId, conversations.length, createNewConversation]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [currentConversation?.messages]);

  if (!currentConversation) {
    return (
      <Box
        sx={{
          height: '100%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Typography variant="h6" color="text.secondary">
          No conversation selected
        </Typography>
      </Box>
    );
  }

  return (
    <Box
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        bgcolor: 'background.default',
      }}
    >
      {/* Model selector bar */}
      <Paper
        elevation={0}
        sx={{
          p: 2,
          borderBottom: 1,
          borderColor: 'divider',
          bgcolor: 'background.paper',
        }}
      >
        <Container maxWidth="lg">
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Typography variant="subtitle2" color="text.secondary">
              Model:
            </Typography>
            <ModelSelector />
          </Box>
        </Container>
      </Paper>

      {/* Messages area */}
      <Box
        sx={{
          flex: 1,
          overflow: 'hidden',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <Container
          maxWidth="lg"
          sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            py: 2,
          }}
        >
          {/* Welcome message for empty conversations */}
          {currentConversation.messages.length === 0 && (
            <Box
              sx={{
                flex: 1,
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                textAlign: 'center',
                gap: 2,
              }}
            >
              <Typography variant="h4" color="primary" gutterBottom>
                Welcome to Advanced Chatbot
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ maxWidth: 600 }}>
                Start a conversation by typing a message, uploading a file, or using voice input.
                This chatbot supports text, speech, and vision capabilities.
              </Typography>
              <Box sx={{ mt: 2 }}>
                <Typography variant="body2" color="text.secondary">
                  Features:
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  🎤 Voice input • 📁 File uploads • 🖼️ Image analysis • 🧠 RAG search
                </Typography>
              </Box>
            </Box>
          )}

          {/* Messages list */}
          {currentConversation.messages.length > 0 && (
            <Box
              sx={{
                flex: 1,
                overflow: 'auto',
                mb: 2,
              }}
            >
              <MessageList
                messages={currentConversation.messages}
                conversationId={currentConversation.id}
              />
              <div ref={messagesEndRef} />
            </Box>
          )}
        </Container>
      </Box>

      {/* Input area */}
      <Paper
        elevation={3}
        sx={{
          borderTop: 1,
          borderColor: 'divider',
          bgcolor: 'background.paper',
        }}
      >
        <Container maxWidth="lg" sx={{ py: 2 }}>
          <ChatInput conversationId={currentConversation.id} />
        </Container>
      </Paper>
    </Box>
  );
};

export default ChatInterface;
