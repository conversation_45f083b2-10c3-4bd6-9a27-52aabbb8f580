import React from 'react';
import {
  FormControl,
  Select,
  MenuItem,
  Box,
  Typography,
  Chip,
} from '@mui/material';
import { useChatStore } from '../../stores/chatStore';

const ModelSelector: React.FC = () => {
  const {
    providers,
    selectedModel,
    selectedProvider,
    selectModel,
  } = useChatStore();

  const handleModelChange = (event: any) => {
    const [provider, model] = event.target.value.split('|');
    selectModel(provider, model);
  };

  const getCurrentValue = () => {
    return selectedProvider && selectedModel ? `${selectedProvider}|${selectedModel}` : '';
  };

  return (
    <FormControl size="small" sx={{ minWidth: 250 }}>
      <Select
        value={getCurrentValue()}
        onChange={handleModelChange}
        displayEmpty
        sx={{
          bgcolor: 'background.paper',
          '& .MuiSelect-select': {
            py: 1,
          },
        }}
      >
        <MenuItem value="" disabled>
          <Typography color="text.secondary">Select a model</Typography>
        </MenuItem>
        
        {providers.map((provider) => [
          // Provider header
          <MenuItem key={`${provider.name}-header`} disabled sx={{ opacity: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="subtitle2" color="primary">
                {provider.displayName}
              </Typography>
              <Chip
                label={provider.available ? 'Available' : 'Unavailable'}
                size="small"
                color={provider.available ? 'success' : 'error'}
                sx={{ height: 20, fontSize: '0.6rem' }}
              />
            </Box>
          </MenuItem>,
          
          // Provider models
          ...provider.models.map((model) => (
            <MenuItem
              key={`${provider.name}|${model.name}`}
              value={`${provider.name}|${model.name}`}
              disabled={!model.available}
              sx={{ pl: 4 }}
            >
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 0.5, width: '100%' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography variant="body2">
                    {model.displayName}
                  </Typography>
                  {!model.available && (
                    <Chip
                      label="Unavailable"
                      size="small"
                      color="error"
                      sx={{ height: 16, fontSize: '0.6rem' }}
                    />
                  )}
                </Box>
                <Typography variant="caption" color="text.secondary">
                  Max tokens: {model.maxTokens.toLocaleString()} • Temperature: {model.temperature}
                </Typography>
              </Box>
            </MenuItem>
          )),
        ])}
        
        {providers.length === 0 && (
          <MenuItem disabled>
            <Typography color="text.secondary">Loading models...</Typography>
          </MenuItem>
        )}
      </Select>
    </FormControl>
  );
};

export default ModelSelector;
