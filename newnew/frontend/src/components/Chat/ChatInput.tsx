import React, { useState, useRef, useCallback } from 'react';
import {
  Box,
  TextField,
  IconButton,
  Button,
  Tooltip,
  CircularProgress,
} from '@mui/material';
import {
  Send as SendIcon,
  Mic as MicIcon,
  MicOff as MicOffIcon,
  AttachFile as AttachFileIcon,
  Image as ImageIcon,
} from '@mui/icons-material';
import { useChatStore } from '../../stores/chatStore';
import { useWebSocket } from '../../hooks/useWebSocket';

interface ChatInputProps {
  conversationId: string;
}

const ChatInput: React.FC<ChatInputProps> = ({ conversationId }) => {
  const [message, setMessage] = useState('');
  const [files, setFiles] = useState<File[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const textFieldRef = useRef<HTMLInputElement>(null);

  const {
    isRecording,
    isProcessingAudio,
    isTyping,
    setRecording,
  } = useChatStore();

  const { sendChatMessage, isConnected } = useWebSocket();

  const handleSendMessage = useCallback(() => {
    if (!message.trim() || !isConnected) return;

    // Send the message
    sendChatMessage(message.trim());
    
    // Clear input
    setMessage('');
    setFiles([]);
    
    // Focus back to input
    textFieldRef.current?.focus();
  }, [message, isConnected, sendChatMessage]);

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(event.target.files || []);
    setFiles(prev => [...prev, ...selectedFiles]);
    
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleMicClick = () => {
    if (isRecording) {
      // Stop recording
      setRecording(false);
      // TODO: Implement actual audio recording stop
    } else {
      // Start recording
      setRecording(true);
      // TODO: Implement actual audio recording start
    }
  };

  const removeFile = (index: number) => {
    setFiles(prev => prev.filter((_, i) => i !== index));
  };

  const canSend = message.trim().length > 0 && isConnected && !isTyping;

  return (
    <Box>
      {/* File previews */}
      {files.length > 0 && (
        <Box sx={{ mb: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          {files.map((file, index) => (
            <Box
              key={index}
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 1,
                p: 1,
                bgcolor: 'background.default',
                borderRadius: 1,
                border: 1,
                borderColor: 'divider',
              }}
            >
              <ImageIcon fontSize="small" />
              <span style={{ fontSize: '0.875rem' }}>
                {file.name.length > 20 ? file.name.slice(0, 20) + '...' : file.name}
              </span>
              <IconButton
                size="small"
                onClick={() => removeFile(index)}
                sx={{ ml: 1 }}
              >
                ×
              </IconButton>
            </Box>
          ))}
        </Box>
      )}

      {/* Input area */}
      <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>
        {/* File upload button */}
        <Tooltip title="Attach file">
          <IconButton
            onClick={() => fileInputRef.current?.click()}
            disabled={!isConnected}
            sx={{ mb: 0.5 }}
          >
            <AttachFileIcon />
          </IconButton>
        </Tooltip>

        {/* Hidden file input */}
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*,.pdf,.txt,.docx"
          style={{ display: 'none' }}
          onChange={handleFileSelect}
        />

        {/* Text input */}
        <TextField
          ref={textFieldRef}
          fullWidth
          multiline
          maxRows={4}
          placeholder={
            isConnected 
              ? "Type your message..." 
              : "Connecting to server..."
          }
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyPress={handleKeyPress}
          disabled={!isConnected || isTyping}
          variant="outlined"
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: 3,
              bgcolor: 'background.paper',
            },
          }}
        />

        {/* Voice input button */}
        <Tooltip title={isRecording ? "Stop recording" : "Start voice input"}>
          <IconButton
            onClick={handleMicClick}
            disabled={!isConnected || isProcessingAudio}
            sx={{
              mb: 0.5,
              bgcolor: isRecording ? 'error.main' : 'transparent',
              color: isRecording ? 'error.contrastText' : 'inherit',
              '&:hover': {
                bgcolor: isRecording ? 'error.dark' : 'action.hover',
              },
            }}
          >
            {isProcessingAudio ? (
              <CircularProgress size={20} />
            ) : isRecording ? (
              <MicOffIcon />
            ) : (
              <MicIcon />
            )}
          </IconButton>
        </Tooltip>

        {/* Send button */}
        <Tooltip title="Send message">
          <span>
            <IconButton
              onClick={handleSendMessage}
              disabled={!canSend}
              sx={{
                mb: 0.5,
                bgcolor: canSend ? 'primary.main' : 'action.disabled',
                color: canSend ? 'primary.contrastText' : 'action.disabled',
                '&:hover': {
                  bgcolor: canSend ? 'primary.dark' : 'action.disabled',
                },
                '&:disabled': {
                  bgcolor: 'action.disabled',
                  color: 'action.disabled',
                },
              }}
            >
              {isTyping ? (
                <CircularProgress size={20} color="inherit" />
              ) : (
                <SendIcon />
              )}
            </IconButton>
          </span>
        </Tooltip>
      </Box>

      {/* Status indicators */}
      {isTyping && (
        <Box sx={{ mt: 1, display: 'flex', alignItems: 'center', gap: 1 }}>
          <CircularProgress size={12} />
          <span style={{ fontSize: '0.875rem', color: 'text.secondary' }}>
            AI is typing...
          </span>
        </Box>
      )}
    </Box>
  );
};

export default ChatInput;
