import React from 'react';
import {
  App<PERSON><PERSON>,
  Toolbar,
  Typography,
  IconButton,
  Box,
  Chip,
  Tooltip,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Settings as SettingsIcon,
  CloudOff as CloudOffIcon,
  Cloud as CloudIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useChatStore } from '../../stores/chatStore';
import { useWebSocket } from '../../hooks/useWebSocket';

const Header: React.FC = () => {
  const navigate = useNavigate();
  const { isConnected } = useWebSocket();
  const {
    setSidebarOpen,
    sidebarOpen,
    selectedModel,
    selectedProvider,
    providers,
  } = useChatStore();

  const handleMenuClick = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleSettingsClick = () => {
    navigate('/settings');
  };

  // Get current model display name
  const getCurrentModelName = () => {
    const provider = providers.find(p => p.name === selectedProvider);
    const model = provider?.models.find(m => m.name === selectedModel);
    return model?.displayName || selectedModel || 'No model selected';
  };

  return (
    <AppBar 
      position="static" 
      elevation={1}
      sx={{ 
        bgcolor: 'background.paper',
        color: 'text.primary',
        borderBottom: 1,
        borderColor: 'divider',
      }}
    >
      <Toolbar>
        {/* Menu button */}
        <IconButton
          edge="start"
          color="inherit"
          aria-label="menu"
          onClick={handleMenuClick}
          sx={{ mr: 2 }}
        >
          <MenuIcon />
        </IconButton>

        {/* Title */}
        <Typography 
          variant="h6" 
          component="div" 
          sx={{ 
            flexGrow: 1,
            fontWeight: 500,
          }}
        >
          Advanced Chatbot
        </Typography>

        {/* Current model indicator */}
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mr: 2 }}>
          <Chip
            label={getCurrentModelName()}
            size="small"
            variant="outlined"
            sx={{
              fontSize: '0.75rem',
              height: 24,
            }}
          />
          
          {/* Connection status */}
          <Tooltip title={isConnected ? 'Connected' : 'Disconnected'}>
            <IconButton size="small" color={isConnected ? 'success' : 'error'}>
              {isConnected ? <CloudIcon /> : <CloudOffIcon />}
            </IconButton>
          </Tooltip>
        </Box>

        {/* Settings button */}
        <Tooltip title="Settings">
          <IconButton
            color="inherit"
            aria-label="settings"
            onClick={handleSettingsClick}
          >
            <SettingsIcon />
          </IconButton>
        </Tooltip>
      </Toolbar>
    </AppBar>
  );
};

export default Header;
