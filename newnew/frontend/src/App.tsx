import React, { useEffect } from 'react';
import { Routes, Route } from 'react-router-dom';
import { Box, Container } from '@mui/material';
import { useChatStore } from './stores/chatStore';
import { useWebSocket } from './hooks/useWebSocket';
import Header from './components/Layout/Header';
import Sidebar from './components/Layout/Sidebar';
import ChatInterface from './components/Chat/ChatInterface';
import Settings from './components/Settings/Settings';
import './App.css';

function App() {
  const { initializeApp } = useChatStore();
  const { connect, disconnect, isConnected } = useWebSocket();

  useEffect(() => {
    // Initialize the application
    initializeApp();
    
    // Connect to WebSocket
    connect();
    
    // Cleanup on unmount
    return () => {
      disconnect();
    };
  }, [initializeApp, connect, disconnect]);

  // Handle Electron menu events
  useEffect(() => {
    if (window.electronAPI) {
      const handleNewConversation = () => {
        useChatStore.getState().createNewConversation();
      };

      const handleOpenFile = (_event: any, filePath: string) => {
        // Handle file opening
        console.log('Opening file:', filePath);
      };

      const handleSettings = () => {
        // Navigate to settings
        window.location.hash = '/settings';
      };

      window.electronAPI.onMenuNewConversation(handleNewConversation);
      window.electronAPI.onMenuOpenFile(handleOpenFile);
      window.electronAPI.onMenuSettings(handleSettings);

      return () => {
        window.electronAPI.removeAllListeners('menu-new-conversation');
        window.electronAPI.removeAllListeners('menu-open-file');
        window.electronAPI.removeAllListeners('menu-settings');
      };
    }
  }, []);

  return (
    <Box className="App" sx={{ display: 'flex', height: '100vh' }}>
      {/* Sidebar */}
      <Sidebar />
      
      {/* Main content */}
      <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column' }}>
        {/* Header */}
        <Header />
        
        {/* Content area */}
        <Box sx={{ flexGrow: 1, overflow: 'hidden' }}>
          <Routes>
            <Route path="/" element={<ChatInterface />} />
            <Route path="/settings" element={<Settings />} />
          </Routes>
        </Box>
      </Box>
      
      {/* Connection status indicator */}
      {!isConnected && (
        <Box
          sx={{
            position: 'fixed',
            top: 16,
            right: 16,
            bgcolor: 'error.main',
            color: 'white',
            px: 2,
            py: 1,
            borderRadius: 1,
            zIndex: 9999,
          }}
        >
          Disconnected from server
        </Box>
      )}
    </Box>
  );
}

export default App;
