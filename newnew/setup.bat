@echo off
REM Advanced Multimodal Chatbot Setup Script for Windows
REM This script sets up both backend and frontend components

setlocal enabledelayedexpansion

echo 🚀 Setting up Advanced Multimodal Chatbot...

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed. Please install Python 3.11 or later.
    echo Download from: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python found

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js is not installed. Please install Node.js 18 or later.
    echo Download from: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js found

REM Check if npm is installed
npm --version >nul 2>&1
if errorlevel 1 (
    echo ❌ npm is not installed. Please install npm.
    pause
    exit /b 1
)

echo ✅ npm found

REM Check if uv is installed
uv --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️ uv is not installed. Installing uv...
    powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
    
    REM Refresh PATH
    call refreshenv
    
    uv --version >nul 2>&1
    if errorlevel 1 (
        echo ❌ Failed to install uv. Please install it manually.
        echo Visit: https://github.com/astral-sh/uv
        pause
        exit /b 1
    )
)

echo ✅ uv found

REM Setup backend
echo 📦 Setting up backend...
cd backend

echo Installing Python dependencies...
uv sync
if errorlevel 1 (
    echo ❌ Failed to install Python dependencies
    pause
    exit /b 1
)

REM Copy environment file
if not exist .env (
    echo Creating .env file...
    copy .env.example .env
    echo ⚠️ Please edit backend\.env file with your API keys and configuration
)

REM Create necessary directories
if not exist data mkdir data
if not exist data\uploads mkdir data\uploads
if not exist data\chromadb mkdir data\chromadb
if not exist logs mkdir logs

echo ✅ Backend setup completed
cd ..

REM Setup frontend
echo 🎨 Setting up frontend...
cd frontend

echo Installing Node.js dependencies...
npm install
if errorlevel 1 (
    echo ❌ Failed to install Node.js dependencies
    pause
    exit /b 1
)

REM Install electron-is-dev if not present
npm list electron-is-dev >nul 2>&1
if errorlevel 1 (
    npm install electron-is-dev
)

echo ✅ Frontend setup completed
cd ..

REM Create startup scripts
echo 📝 Creating startup scripts...

REM Backend startup script
echo @echo off > start-backend.bat
echo echo Starting chatbot backend... >> start-backend.bat
echo cd backend >> start-backend.bat
echo uv run uvicorn src.main:app --reload --host localhost --port 8000 >> start-backend.bat

REM Frontend startup script
echo @echo off > start-frontend.bat
echo echo Starting chatbot frontend... >> start-frontend.bat
echo cd frontend >> start-frontend.bat
echo npm start >> start-frontend.bat

REM Electron startup script
echo @echo off > start-electron.bat
echo echo Starting chatbot Electron app... >> start-electron.bat
echo cd frontend >> start-electron.bat
echo npm run electron-dev >> start-electron.bat

REM Combined startup script
echo @echo off > start-all.bat
echo echo Starting Advanced Multimodal Chatbot... >> start-all.bat
echo echo Starting backend... >> start-all.bat
echo start "Backend" cmd /k "cd backend && uv run uvicorn src.main:app --reload --host localhost --port 8000" >> start-all.bat
echo timeout /t 5 /nobreak ^>nul >> start-all.bat
echo echo Starting frontend... >> start-all.bat
echo start "Frontend" cmd /k "cd frontend && npm start" >> start-all.bat
echo echo ✅ Chatbot is starting up! >> start-all.bat
echo echo 📱 Web interface: http://localhost:3000 >> start-all.bat
echo echo 🔧 API docs: http://localhost:8000/docs >> start-all.bat
echo echo Press any key to exit... >> start-all.bat
echo pause >> start-all.bat

echo ✅ Startup scripts created

REM Check for Ollama
echo 🤖 Checking for Ollama...
ollama --version >nul 2>&1
if errorlevel 1 (
    echo ⚠️ Ollama is not installed.
    echo Would you like to install Ollama for local LLM support? (y/n)
    set /p response=
    if /i "!response!"=="y" (
        echo Please download and install Ollama from: https://ollama.ai/download/windows
        echo After installation, run: ollama pull llama3.1:latest
        pause
    )
) else (
    echo ✅ Ollama found
    echo Pulling default model (llama3.1:latest)...
    ollama pull llama3.1:latest
)

echo.
echo 🎉 Setup completed successfully!
echo.
echo 📋 Next steps:
echo 1. Edit backend\.env with your API keys
echo 2. Start the application:
echo    • All services: start-all.bat
echo    • Backend only: start-backend.bat
echo    • Frontend only: start-frontend.bat
echo    • Electron app: start-electron.bat
echo.
echo 🌐 Access points:
echo    • Web interface: http://localhost:3000
echo    • API documentation: http://localhost:8000/docs
echo    • API health check: http://localhost:8000/health
echo.
echo 📚 For more information, see README.md
echo.
pause
