# Advanced Multimodal Chatbot Configuration

# Server Configuration
server:
  host: "localhost"
  port: 8000
  cors_origins: ["http://localhost:3000", "http://127.0.0.1:3000"]
  debug: true

# Database Configuration
database:
  url: "sqlite:///./chatbot.db"
  # For production, use PostgreSQL:
  # url: "postgresql://user:password@localhost/chatbot"

# LLM Configuration
llm:
  default_provider: "ollama"
  
  providers:
    ollama:
      base_url: "http://localhost:11434/v1"
      models:
        - name: "llama3.1:latest"
          display_name: "Llama 3.1 (8B)"
          max_tokens: 4096
          temperature: 0.7
        - name: "qwen2.5:latest"
          display_name: "Qwen 2.5 (7B)"
          max_tokens: 4096
          temperature: 0.7
        - name: "mistral:latest"
          display_name: "Mistral (7B)"
          max_tokens: 4096
          temperature: 0.7
    
    openai:
      api_key: "${OPENAI_API_KEY}"
      models:
        - name: "gpt-4o"
          display_name: "GPT-4o"
          max_tokens: 4096
          temperature: 0.7
        - name: "gpt-4o-mini"
          display_name: "GPT-4o Mini"
          max_tokens: 4096
          temperature: 0.7
    
    claude:
      api_key: "${ANTHROPIC_API_KEY}"
      models:
        - name: "claude-3-5-sonnet-20241022"
          display_name: "Claude 3.5 Sonnet"
          max_tokens: 4096
          temperature: 0.7
        - name: "claude-3-haiku-20240307"
          display_name: "Claude 3 Haiku"
          max_tokens: 4096
          temperature: 0.7
    
    gemini:
      api_key: "${GOOGLE_API_KEY}"
      models:
        - name: "gemini-2.0-flash-exp"
          display_name: "Gemini 2.0 Flash"
          max_tokens: 4096
          temperature: 0.7

# ASR (Automatic Speech Recognition) Configuration
asr:
  default_engine: "whisper_local"
  
  engines:
    whisper_local:
      model: "base"
      device: "cpu"
      language: "auto"
    
    whisper_openai:
      api_key: "${OPENAI_API_KEY}"
      model: "whisper-1"
    
    azure_speech:
      api_key: "${AZURE_SPEECH_KEY}"
      region: "${AZURE_SPEECH_REGION}"
      language: "en-US"

# TTS (Text-to-Speech) Configuration
tts:
  default_engine: "edge_tts"
  
  engines:
    edge_tts:
      voice: "en-US-AriaNeural"
      rate: "+0%"
      pitch: "+0Hz"
    
    openai_tts:
      api_key: "${OPENAI_API_KEY}"
      model: "tts-1"
      voice: "alloy"
    
    azure_tts:
      api_key: "${AZURE_SPEECH_KEY}"
      region: "${AZURE_SPEECH_REGION}"
      voice: "en-US-AriaNeural"

# VAD (Voice Activity Detection) Configuration
vad:
  engine: "silero"
  threshold: 0.5
  min_speech_duration: 0.25
  min_silence_duration: 0.5

# RAG (Retrieval-Augmented Generation) Configuration
rag:
  enabled: true
  vector_store: "chromadb"
  embedding_model: "sentence-transformers/all-MiniLM-L6-v2"
  chunk_size: 1000
  chunk_overlap: 200
  max_results: 5
  
  chromadb:
    persist_directory: "./data/chromadb"
    collection_name: "documents"

# Audio Configuration
audio:
  sample_rate: 16000
  channels: 1
  chunk_size: 1024
  format: "wav"

# File Upload Configuration
uploads:
  max_file_size: 10485760  # 10MB
  allowed_extensions: [".txt", ".pdf", ".docx", ".png", ".jpg", ".jpeg", ".gif", ".webp"]
  upload_directory: "./data/uploads"

# Logging Configuration
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}"
  file: "./logs/chatbot.log"
  rotation: "10 MB"
  retention: "30 days"

# Redis Configuration (for Celery)
redis:
  url: "redis://localhost:6379/0"

# Security Configuration
security:
  secret_key: "${SECRET_KEY}"
  algorithm: "HS256"
  access_token_expire_minutes: 30
