2025-07-04 15:46:37 | INFO     | src.main:lifespan:22 | Starting chatbot backend...
2025-07-04 15:46:37 | INFO     | src.main:lifespan:33 | Chatbot backend started successfully!
2025-07-04 22:27:31 | INFO     | src.main:lifespan:22 | Starting chatbot backend...
2025-07-04 22:27:31 | INFO     | src.main:lifespan:33 | Chatbot backend started successfully!
2025-07-04 22:27:43 | INFO     | src.websocket.chat:connect:31 | Client client_1751639263474_j8zo6uz29 connected
2025-07-04 22:27:43 | INFO     | src.websocket.chat:connect:31 | Client client_1751639263475_y1as057ke connected
2025-07-04 22:27:43 | INFO     | src.websocket.chat:disconnect:39 | Client client_1751639263474_j8zo6uz29 disconnected
2025-07-04 22:27:43 | ERROR    | src.services.llm:chat_stream:82 | Error in chat stream for claude/claude-3-5-sonnet-20241022: Invalid type for url.  Expected str or httpx.URL, got <class 'NoneType'>: None
2025-07-04 22:27:43 | ERROR    | src.services.llm:chat_stream:82 | Error in chat stream for claude/claude-3-haiku-20240307: Invalid type for url.  Expected str or httpx.URL, got <class 'NoneType'>: None
2025-07-04 22:27:47 | ERROR    | src.services.llm:chat_stream:82 | Error in chat stream for openai/gpt-4o: Invalid type for url.  Expected str or httpx.URL, got <class 'NoneType'>: None
2025-07-04 22:27:47 | ERROR    | src.services.llm:chat_stream:82 | Error in chat stream for openai/gpt-4o-mini: Invalid type for url.  Expected str or httpx.URL, got <class 'NoneType'>: None
2025-07-04 22:29:37 | INFO     | src.websocket.chat:disconnect:39 | Client client_1751639263475_y1as057ke disconnected
2025-07-04 22:29:37 | INFO     | src.main:lifespan:38 | Shutting down chatbot backend...
