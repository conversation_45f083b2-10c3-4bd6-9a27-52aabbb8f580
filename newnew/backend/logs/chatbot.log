2025-07-04 15:46:37 | INFO     | src.main:lifespan:22 | Starting chatbot backend...
2025-07-04 15:46:37 | INFO     | src.main:lifespan:33 | Chatbot backend started successfully!
2025-07-04 22:27:31 | INFO     | src.main:lifespan:22 | Starting chatbot backend...
2025-07-04 22:27:31 | INFO     | src.main:lifespan:33 | Chatbot backend started successfully!
2025-07-04 22:27:43 | INFO     | src.websocket.chat:connect:31 | Client client_1751639263474_j8zo6uz29 connected
2025-07-04 22:27:43 | INFO     | src.websocket.chat:connect:31 | Client client_1751639263475_y1as057ke connected
2025-07-04 22:27:43 | INFO     | src.websocket.chat:disconnect:39 | Client client_1751639263474_j8zo6uz29 disconnected
2025-07-04 22:27:43 | ERROR    | src.services.llm:chat_stream:82 | Error in chat stream for claude/claude-3-5-sonnet-20241022: Invalid type for url.  Expected str or httpx.URL, got <class 'NoneType'>: None
2025-07-04 22:27:43 | ERROR    | src.services.llm:chat_stream:82 | Error in chat stream for claude/claude-3-haiku-20240307: Invalid type for url.  Expected str or httpx.URL, got <class 'NoneType'>: None
2025-07-04 22:27:47 | ERROR    | src.services.llm:chat_stream:82 | Error in chat stream for openai/gpt-4o: Invalid type for url.  Expected str or httpx.URL, got <class 'NoneType'>: None
2025-07-04 22:27:47 | ERROR    | src.services.llm:chat_stream:82 | Error in chat stream for openai/gpt-4o-mini: Invalid type for url.  Expected str or httpx.URL, got <class 'NoneType'>: None
2025-07-04 22:29:37 | INFO     | src.websocket.chat:disconnect:39 | Client client_1751639263475_y1as057ke disconnected
2025-07-04 22:29:37 | INFO     | src.main:lifespan:38 | Shutting down chatbot backend...
2025-07-04 22:33:26 | INFO     | src.main:lifespan:22 | Starting chatbot backend...
2025-07-04 22:33:26 | INFO     | src.main:lifespan:33 | Chatbot backend started successfully!
2025-07-04 22:35:09 | INFO     | src.websocket.chat:connect:31 | Client client_1751639709762_u3rtle0pw connected
2025-07-04 22:35:09 | INFO     | src.websocket.chat:connect:31 | Client client_1751639709765_po2l6tuxo connected
2025-07-04 22:35:09 | INFO     | src.websocket.chat:disconnect:39 | Client client_1751639709762_u3rtle0pw disconnected
2025-07-04 22:35:09 | INFO     | src.services.llm:get_ollama_models:288 | Found 17 Ollama models
2025-07-04 22:35:09 | ERROR    | src.services.llm:chat_stream:82 | Error in chat stream for claude/claude-3-5-sonnet-20241022: Invalid type for url.  Expected str or httpx.URL, got <class 'NoneType'>: None
2025-07-04 22:35:09 | ERROR    | src.services.llm:chat_stream:82 | Error in chat stream for claude/claude-3-haiku-20240307: Invalid type for url.  Expected str or httpx.URL, got <class 'NoneType'>: None
2025-07-04 22:35:09 | ERROR    | src.services.llm:chat_stream:82 | Error in chat stream for openai/gpt-4o: Invalid type for url.  Expected str or httpx.URL, got <class 'NoneType'>: None
2025-07-04 22:35:09 | ERROR    | src.services.llm:chat_stream:82 | Error in chat stream for openai/gpt-4o-mini: Invalid type for url.  Expected str or httpx.URL, got <class 'NoneType'>: None
2025-07-04 22:35:12 | INFO     | src.websocket.chat:connect:31 | Client client_1751639712279_z73bptrrr connected
2025-07-04 22:35:12 | INFO     | src.websocket.chat:connect:31 | Client client_1751639712282_gltb1irpi connected
2025-07-04 22:35:12 | INFO     | src.websocket.chat:disconnect:39 | Client client_1751639712279_z73bptrrr disconnected
2025-07-04 22:35:12 | INFO     | src.services.llm:get_ollama_models:288 | Found 17 Ollama models
2025-07-04 22:35:12 | ERROR    | src.services.llm:chat_stream:82 | Error in chat stream for claude/claude-3-5-sonnet-20241022: Invalid type for url.  Expected str or httpx.URL, got <class 'NoneType'>: None
2025-07-04 22:35:12 | ERROR    | src.services.llm:chat_stream:82 | Error in chat stream for claude/claude-3-haiku-20240307: Invalid type for url.  Expected str or httpx.URL, got <class 'NoneType'>: None
2025-07-04 22:35:12 | ERROR    | src.services.llm:chat_stream:82 | Error in chat stream for openai/gpt-4o: Invalid type for url.  Expected str or httpx.URL, got <class 'NoneType'>: None
2025-07-04 22:35:12 | ERROR    | src.services.llm:chat_stream:82 | Error in chat stream for openai/gpt-4o-mini: Invalid type for url.  Expected str or httpx.URL, got <class 'NoneType'>: None
2025-07-04 22:37:07 | INFO     | src.websocket.chat:disconnect:39 | Client client_1751639712282_gltb1irpi disconnected
2025-07-04 22:37:07 | INFO     | src.websocket.chat:disconnect:39 | Client client_1751639709765_po2l6tuxo disconnected
2025-07-04 22:37:38 | INFO     | src.main:lifespan:38 | Shutting down chatbot backend...
2025-07-04 22:37:39 | INFO     | src.main:lifespan:22 | Starting chatbot backend...
2025-07-04 22:37:39 | INFO     | src.main:lifespan:33 | Chatbot backend started successfully!
2025-07-04 22:38:10 | INFO     | src.main:lifespan:38 | Shutting down chatbot backend...
