[project]
name = "chatbot-backend"
version = "0.1.0"
description = "Advanced multimodal chatbot backend"
authors = [
    {name = "Dev<PERSON>per", email = "<EMAIL>"}
]
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "websockets>=12.0",
    "sqlalchemy>=2.0.0",
    "alembic>=1.12.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    "python-multipart>=0.0.6",
    "aiofiles>=23.2.1",
    "python-jose[cryptography]>=3.3.0",
    "passlib[bcrypt]>=1.7.4",
    "httpx>=0.25.0",
    "openai>=1.3.0",
    "anthropic>=0.7.0",
    "google-generativeai>=0.3.0",
    "chromadb>=0.4.0",
    "langchain>=0.1.0",
    "langchain-community>=0.0.10",
    "sentence-transformers>=2.2.0",
    "numpy>=1.24.0",
    "scipy>=1.11.0",
    "librosa>=0.10.0",
    "soundfile>=0.12.0",
    "whisper>=1.1.10",
    "openai-whisper>=20231117",
    "faster-whisper>=0.10.0",
    "edge-tts>=6.1.0",
    "azure-cognitiveservices-speech>=1.34.0",
    "silero-vad>=4.0.0",
    "torch>=2.1.0",
    "torchaudio>=2.1.0",
    "transformers>=4.35.0",
    "PyPDF2>=3.0.0",
    "python-docx>=1.1.0",
    "beautifulsoup4>=4.12.0",
    "requests>=2.31.0",
    "redis>=5.0.0",
    "celery>=5.3.0",
    "loguru>=0.7.0",
    "python-dotenv>=1.0.0",
    "pyyaml>=6.0.1",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.9.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.6.0",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.black]
line-length = 88
target-version = ['py311']

[tool.isort]
profile = "black"
multi_line_output = 3

[tool.mypy]
python_version = "3.11"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true

[tool.pytest.ini_options]
asyncio_mode = "auto"
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
