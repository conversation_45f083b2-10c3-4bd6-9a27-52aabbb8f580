"""File upload and management API endpoints."""

import os
import uuid
from pathlib import Path
from typing import List

from fastapi import APIRouter, Depends, File, HTTPException, UploadFile
from pydantic import BaseModel
from sqlalchemy.orm import Session

from ..config import get_config
from ..database import get_db, Document
from ..services.rag import RAGService

router = APIRouter()


class FileResponse(BaseModel):
    """File upload response."""
    id: int
    filename: str
    file_type: str
    file_size: int
    processed: bool
    created_at: str

    class Config:
        from_attributes = True


class DocumentResponse(BaseModel):
    """Document response."""
    id: int
    filename: str
    file_type: str
    file_size: int
    processed: bool
    created_at: str
    metadata: dict

    class Config:
        from_attributes = True


@router.post("/upload", response_model=FileResponse)
async def upload_file(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """Upload a file."""
    config = get_config()
    
    # Check file size
    if file.size > config.uploads.max_file_size:
        raise HTTPException(
            status_code=413,
            detail=f"File too large. Maximum size is {config.uploads.max_file_size} bytes"
        )
    
    # Check file extension
    file_extension = Path(file.filename).suffix.lower()
    if file_extension not in config.uploads.allowed_extensions:
        raise HTTPException(
            status_code=400,
            detail=f"File type not allowed. Allowed types: {config.uploads.allowed_extensions}"
        )
    
    # Generate unique filename
    unique_filename = f"{uuid.uuid4()}{file_extension}"
    file_path = Path(config.uploads.upload_directory) / unique_filename
    
    # Save file
    try:
        with open(file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to save file: {str(e)}")
    
    # Create database record
    db_document = Document(
        filename=file.filename,
        file_path=str(file_path),
        file_type=file_extension,
        file_size=file.size,
        processed=False
    )
    db.add(db_document)
    db.commit()
    db.refresh(db_document)
    
    # Process file for RAG (async)
    try:
        rag_service = RAGService()
        await rag_service.process_document(db_document.id, str(file_path))
        
        # Update processed status
        db_document.processed = True
        db.commit()
    except Exception as e:
        # Log error but don't fail the upload
        print(f"Failed to process document for RAG: {str(e)}")
    
    return db_document


@router.get("/documents", response_model=List[DocumentResponse])
async def get_documents(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """Get all uploaded documents."""
    documents = db.query(Document).offset(skip).limit(limit).all()
    return documents


@router.get("/documents/{document_id}", response_model=DocumentResponse)
async def get_document(
    document_id: int,
    db: Session = Depends(get_db)
):
    """Get a specific document."""
    document = db.query(Document).filter(Document.id == document_id).first()
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    return document


@router.delete("/documents/{document_id}")
async def delete_document(
    document_id: int,
    db: Session = Depends(get_db)
):
    """Delete a document."""
    document = db.query(Document).filter(Document.id == document_id).first()
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    # Delete file from filesystem
    try:
        if os.path.exists(document.file_path):
            os.remove(document.file_path)
    except Exception as e:
        print(f"Failed to delete file: {str(e)}")
    
    # Remove from vector store
    try:
        rag_service = RAGService()
        await rag_service.remove_document(document_id)
    except Exception as e:
        print(f"Failed to remove document from vector store: {str(e)}")
    
    # Delete from database
    db.delete(document)
    db.commit()
    
    return {"message": "Document deleted successfully"}


@router.post("/documents/{document_id}/reprocess")
async def reprocess_document(
    document_id: int,
    db: Session = Depends(get_db)
):
    """Reprocess a document for RAG."""
    document = db.query(Document).filter(Document.id == document_id).first()
    if not document:
        raise HTTPException(status_code=404, detail="Document not found")
    
    try:
        rag_service = RAGService()
        await rag_service.process_document(document_id, document.file_path)
        
        # Update processed status
        document.processed = True
        db.commit()
        
        return {"message": "Document reprocessed successfully"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to reprocess document: {str(e)}")


@router.get("/search")
async def search_documents(
    query: str,
    limit: int = 5
):
    """Search documents using RAG."""
    try:
        rag_service = RAGService()
        results = await rag_service.search(query, limit)
        return {"results": results}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")
