"""Main FastAPI application."""

import os
from contextlib import asynccontextmanager
from pathlib import Path

from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from loguru import logger

from .api import router as api_router
from .config import get_config
from .database import init_db
from .websocket import router as websocket_router


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting chatbot backend...")
    
    # Initialize database
    await init_db()
    
    # Create necessary directories
    config = get_config()
    os.makedirs(config.uploads.upload_directory, exist_ok=True)
    os.makedirs(config.rag.chromadb.persist_directory, exist_ok=True)
    os.makedirs("logs", exist_ok=True)
    
    logger.info("Chatbot backend started successfully!")
    
    yield
    
    # Shutdown
    logger.info("Shutting down chatbot backend...")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    config = get_config()
    
    # Configure logging
    logger.remove()
    logger.add(
        config.logging.file,
        level=config.logging.level,
        format=config.logging.format,
        rotation=config.logging.rotation,
        retention=config.logging.retention,
    )
    logger.add(
        lambda msg: print(msg, end=""),
        level=config.logging.level,
        format=config.logging.format,
        colorize=True,
    )
    
    app = FastAPI(
        title="Advanced Multimodal Chatbot API",
        description="A modern chatbot with speech, vision, and text capabilities",
        version="0.1.0",
        lifespan=lifespan,
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=config.server.cors_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Include routers
    app.include_router(api_router, prefix="/api/v1")
    app.include_router(websocket_router, prefix="/ws")
    
    # Mount static files for uploads
    if Path(config.uploads.upload_directory).exists():
        app.mount(
            "/uploads",
            StaticFiles(directory=config.uploads.upload_directory),
            name="uploads",
        )
    
    @app.get("/")
    async def root():
        """Root endpoint."""
        return {
            "message": "Advanced Multimodal Chatbot API",
            "version": "0.1.0",
            "status": "running",
        }
    
    @app.get("/health")
    async def health():
        """Health check endpoint."""
        return {"status": "healthy"}
    
    return app


# Create the app instance
app = create_app()


if __name__ == "__main__":
    import uvicorn
    
    config = get_config()
    uvicorn.run(
        "src.main:app",
        host=config.server.host,
        port=config.server.port,
        reload=config.server.debug,
        log_level=config.logging.level.lower(),
    )
