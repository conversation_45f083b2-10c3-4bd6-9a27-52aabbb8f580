"""LLM service for handling different language model providers."""

import asyncio
from typing import List, Dict, Any, AsyncIterator, Optional

import httpx
from loguru import logger

from ..config import get_config


class LLMService:
    """Service for managing LLM interactions."""
    
    def __init__(self):
        self.config = get_config()
        self.clients = {}
    
    def get_client(self, provider: str) -> httpx.AsyncClient:
        """Get HTTP client for a provider."""
        if provider not in self.clients:
            provider_config = self.config.llm.providers.get(provider)
            if not provider_config:
                raise ValueError(f"Provider {provider} not configured")
            
            headers = {}
            if provider_config.api_key:
                if provider == "openai":
                    headers["Authorization"] = f"Bearer {provider_config.api_key}"
                elif provider == "claude":
                    headers["x-api-key"] = provider_config.api_key
                    headers["anthropic-version"] = "2023-06-01"
                elif provider == "gemini":
                    # Gemini uses API key in URL params
                    pass
            
            self.clients[provider] = httpx.AsyncClient(
                base_url=provider_config.base_url,
                headers=headers,
                timeout=60.0
            )
        
        return self.clients[provider]
    
    async def chat_stream(
        self,
        messages: List[Dict[str, str]],
        model: str,
        provider: str,
        context: str = "",
        images: List[str] = None,
        temperature: float = 0.7,
        max_tokens: int = 4096
    ) -> AsyncIterator[str]:
        """Stream chat completion from LLM."""
        
        # Add context to messages if provided
        if context:
            context_message = {
                "role": "system",
                "content": f"Context information:\n{context}\n\nPlease use this context to help answer the user's question."
            }
            messages = [context_message] + messages
        
        try:
            if provider == "ollama":
                async for chunk in self._stream_ollama(messages, model, temperature, max_tokens):
                    yield chunk
            elif provider == "openai":
                async for chunk in self._stream_openai(messages, model, temperature, max_tokens, images):
                    yield chunk
            elif provider == "claude":
                async for chunk in self._stream_claude(messages, model, temperature, max_tokens, images):
                    yield chunk
            elif provider == "gemini":
                async for chunk in self._stream_gemini(messages, model, temperature, max_tokens, images):
                    yield chunk
            else:
                raise ValueError(f"Unsupported provider: {provider}")
        
        except Exception as e:
            logger.error(f"Error in chat stream for {provider}/{model}: {str(e)}")
            yield f"Error: {str(e)}"
    
    async def _stream_ollama(
        self,
        messages: List[Dict[str, str]],
        model: str,
        temperature: float,
        max_tokens: int
    ) -> AsyncIterator[str]:
        """Stream from Ollama."""
        client = self.get_client("ollama")
        
        payload = {
            "model": model,
            "messages": messages,
            "stream": True,
            "options": {
                "temperature": temperature,
                "num_predict": max_tokens
            }
        }
        
        async with client.stream("POST", "/chat/completions", json=payload) as response:
            async for line in response.aiter_lines():
                if line.startswith("data: "):
                    data = line[6:]
                    if data == "[DONE]":
                        break
                    
                    try:
                        import json
                        chunk = json.loads(data)
                        if "choices" in chunk and chunk["choices"]:
                            delta = chunk["choices"][0].get("delta", {})
                            if "content" in delta:
                                yield delta["content"]
                    except json.JSONDecodeError:
                        continue
    
    async def _stream_openai(
        self,
        messages: List[Dict[str, str]],
        model: str,
        temperature: float,
        max_tokens: int,
        images: List[str] = None
    ) -> AsyncIterator[str]:
        """Stream from OpenAI."""
        client = self.get_client("openai")
        
        # Handle images for vision models
        if images and any(model_name in model.lower() for model_name in ["gpt-4o", "gpt-4-vision"]):
            # Convert text + images to vision format
            last_message = messages[-1]
            content = [{"type": "text", "text": last_message["content"]}]
            
            for image_url in images:
                content.append({
                    "type": "image_url",
                    "image_url": {"url": image_url}
                })
            
            messages[-1]["content"] = content
        
        payload = {
            "model": model,
            "messages": messages,
            "stream": True,
            "temperature": temperature,
            "max_tokens": max_tokens
        }
        
        async with client.stream("POST", "/chat/completions", json=payload) as response:
            async for line in response.aiter_lines():
                if line.startswith("data: "):
                    data = line[6:]
                    if data == "[DONE]":
                        break
                    
                    try:
                        import json
                        chunk = json.loads(data)
                        if "choices" in chunk and chunk["choices"]:
                            delta = chunk["choices"][0].get("delta", {})
                            if "content" in delta:
                                yield delta["content"]
                    except json.JSONDecodeError:
                        continue
    
    async def _stream_claude(
        self,
        messages: List[Dict[str, str]],
        model: str,
        temperature: float,
        max_tokens: int,
        images: List[str] = None
    ) -> AsyncIterator[str]:
        """Stream from Claude."""
        client = self.get_client("claude")
        
        # Claude has different message format
        system_message = ""
        claude_messages = []
        
        for msg in messages:
            if msg["role"] == "system":
                system_message = msg["content"]
            else:
                claude_messages.append(msg)
        
        payload = {
            "model": model,
            "messages": claude_messages,
            "stream": True,
            "temperature": temperature,
            "max_tokens": max_tokens
        }
        
        if system_message:
            payload["system"] = system_message
        
        async with client.stream("POST", "/messages", json=payload) as response:
            async for line in response.aiter_lines():
                if line.startswith("data: "):
                    data = line[6:]
                    
                    try:
                        import json
                        chunk = json.loads(data)
                        if chunk.get("type") == "content_block_delta":
                            delta = chunk.get("delta", {})
                            if "text" in delta:
                                yield delta["text"]
                    except json.JSONDecodeError:
                        continue
    
    async def _stream_gemini(
        self,
        messages: List[Dict[str, str]],
        model: str,
        temperature: float,
        max_tokens: int,
        images: List[str] = None
    ) -> AsyncIterator[str]:
        """Stream from Gemini."""
        # Gemini implementation would go here
        # For now, return a placeholder
        yield "Gemini streaming not implemented yet"
    
    async def get_available_models(self) -> List[Dict[str, Any]]:
        """Get all available models."""
        models = []

        # First, get Ollama models dynamically
        ollama_models = await self.get_ollama_models()
        models.extend(ollama_models)

        # Then add configured models from other providers
        for provider_name, provider_config in self.config.llm.providers.items():
            if provider_name == "ollama":
                continue  # Skip static ollama config, we got them dynamically

            for model_config in provider_config.models:
                models.append({
                    "name": model_config.name,
                    "display_name": model_config.display_name,
                    "provider": provider_name,
                    "max_tokens": model_config.max_tokens,
                    "temperature": model_config.temperature,
                    "available": await self.test_model(provider_name, model_config.name)
                })

        return models

    async def get_ollama_models(self) -> List[Dict[str, Any]]:
        """Get available Ollama models dynamically."""
        models = []

        try:
            # Try to connect to Ollama API
            async with httpx.AsyncClient() as client:
                response = await client.get("http://localhost:11434/api/tags")

                if response.status_code == 200:
                    data = response.json()

                    for model_info in data.get("models", []):
                        model_name = model_info.get("name", "")
                        size_gb = round(model_info.get("size", 0) / (1024**3), 1)

                        # Extract parameter size from details
                        details = model_info.get("details", {})
                        param_size = details.get("parameter_size", "Unknown")

                        models.append({
                            "name": model_name,
                            "display_name": f"{model_name} ({param_size})",
                            "provider": "ollama",
                            "max_tokens": 4096,  # Default for most models
                            "temperature": 0.7,
                            "available": True,  # If it's in the list, it's available
                            "size": f"{size_gb} GB",
                            "parameter_size": param_size
                        })

                    logger.info(f"Found {len(models)} Ollama models")
                else:
                    logger.warning(f"Failed to fetch Ollama models: {response.status_code}")

        except Exception as e:
            logger.warning(f"Could not connect to Ollama: {str(e)}")

        return models

    async def test_model(self, provider: str, model: str) -> bool:
        """Test if a model is available."""
        try:
            # Simple test message
            messages = [{"role": "user", "content": "Hello"}]
            
            # Try to get first chunk
            async for chunk in self.chat_stream(messages, model, provider):
                return True  # If we get any response, model is available
            
            return False
        
        except Exception as e:
            logger.warning(f"Model {provider}/{model} not available: {str(e)}")
            return False
    
    async def close(self):
        """Close all HTTP clients."""
        for client in self.clients.values():
            await client.aclose()
        self.clients.clear()
