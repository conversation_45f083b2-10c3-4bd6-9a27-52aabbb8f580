"""Chat service for managing conversations."""

from typing import List, Dict, Any, Optional
from datetime import datetime

from sqlalchemy.orm import Session

from ..database import SessionLocal, Conversation, Message


class ChatService:
    """Service for managing chat conversations."""
    
    def __init__(self):
        self.db: Optional[Session] = None
    
    def get_db(self) -> Session:
        """Get database session."""
        if self.db is None:
            self.db = SessionLocal()
        return self.db
    
    def close_db(self):
        """Close database session."""
        if self.db:
            self.db.close()
            self.db = None
    
    def create_conversation(self, title: str, metadata: Dict[str, Any] = None) -> Conversation:
        """Create a new conversation."""
        db = self.get_db()
        
        conversation = Conversation(
            title=title,
            metadata=metadata or {}
        )
        
        db.add(conversation)
        db.commit()
        db.refresh(conversation)
        
        return conversation
    
    def get_conversation(self, conversation_id: int) -> Optional[Conversation]:
        """Get a conversation by ID."""
        db = self.get_db()
        return db.query(Conversation).filter(Conversation.id == conversation_id).first()
    
    def get_conversations(self, skip: int = 0, limit: int = 100) -> List[Conversation]:
        """Get all conversations."""
        db = self.get_db()
        return db.query(Conversation).offset(skip).limit(limit).all()
    
    def delete_conversation(self, conversation_id: int) -> bool:
        """Delete a conversation and all its messages."""
        db = self.get_db()
        
        # Delete all messages first
        db.query(Message).filter(Message.conversation_id == conversation_id).delete()
        
        # Delete conversation
        conversation = db.query(Conversation).filter(Conversation.id == conversation_id).first()
        if conversation:
            db.delete(conversation)
            db.commit()
            return True
        
        return False
    
    def add_message(
        self,
        conversation_id: int,
        role: str,
        content: str,
        content_type: str = "text",
        metadata: Dict[str, Any] = None,
        audio_url: str = None,
        audio_duration: int = None,
        image_url: str = None,
        image_caption: str = None
    ) -> Message:
        """Add a message to a conversation."""
        db = self.get_db()
        
        message = Message(
            conversation_id=conversation_id,
            role=role,
            content=content,
            content_type=content_type,
            metadata=metadata or {},
            audio_url=audio_url,
            audio_duration=audio_duration,
            image_url=image_url,
            image_caption=image_caption
        )
        
        db.add(message)
        db.commit()
        db.refresh(message)
        
        # Update conversation timestamp
        conversation = db.query(Conversation).filter(Conversation.id == conversation_id).first()
        if conversation:
            conversation.updated_at = datetime.utcnow()
            db.commit()
        
        return message
    
    def get_messages(
        self,
        conversation_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[Message]:
        """Get messages for a conversation."""
        db = self.get_db()
        return (
            db.query(Message)
            .filter(Message.conversation_id == conversation_id)
            .order_by(Message.created_at)
            .offset(skip)
            .limit(limit)
            .all()
        )
    
    def get_message(self, message_id: int) -> Optional[Message]:
        """Get a message by ID."""
        db = self.get_db()
        return db.query(Message).filter(Message.id == message_id).first()
    
    def update_message(
        self,
        message_id: int,
        content: str = None,
        metadata: Dict[str, Any] = None
    ) -> Optional[Message]:
        """Update a message."""
        db = self.get_db()
        message = db.query(Message).filter(Message.id == message_id).first()
        
        if message:
            if content is not None:
                message.content = content
            if metadata is not None:
                message.metadata = metadata
            
            db.commit()
            db.refresh(message)
        
        return message
    
    def delete_message(self, message_id: int) -> bool:
        """Delete a message."""
        db = self.get_db()
        message = db.query(Message).filter(Message.id == message_id).first()
        
        if message:
            db.delete(message)
            db.commit()
            return True
        
        return False
    
    def get_conversation_context(
        self,
        conversation_id: int,
        max_messages: int = 20
    ) -> List[Dict[str, str]]:
        """Get conversation context for LLM."""
        messages = self.get_messages(conversation_id, limit=max_messages)
        
        context = []
        for message in messages:
            context.append({
                "role": message.role,
                "content": message.content
            })
        
        return context
    
    def search_conversations(self, query: str, limit: int = 10) -> List[Conversation]:
        """Search conversations by title or content."""
        db = self.get_db()
        
        # Search by title
        conversations = (
            db.query(Conversation)
            .filter(Conversation.title.contains(query))
            .limit(limit)
            .all()
        )
        
        return conversations
    
    def __del__(self):
        """Cleanup database connection."""
        self.close_db()
