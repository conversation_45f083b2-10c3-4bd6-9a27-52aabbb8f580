{"Version": 3, "Meta": {"Duration": 9.23, "Fps": 30.0, "FadeInTime": 0.25, "FadeOutTime": 0.25, "Loop": true, "AreBeziersRestricted": true, "CurveCount": 132, "TotalSegmentCount": 619, "TotalPointCount": 1713, "UserDataCount": 0, "TotalUserDataSize": 0}, "Curves": [{"Target": "Parameter", "Id": "ParamAngleX", "Segments": [0, 0, 1, 0.111, 0, 0.222, 0.706, 0.333, -1.7, 1, 0.544, -6.272, 0.756, -25.931, 0.967, -25.931, 1, 1.222, -25.931, 1.478, -22.931, 1.733, -22.931, 1, 1.856, -22.931, 1.978, -24.931, 2.1, -24.931, 1, 2.411, -24.931, 2.722, -20.287, 3.033, -8, 1, 3.111, -4.928, 3.189, 2.21, 3.267, 7, 1, 3.389, 14.527, 3.511, 18, 3.633, 18, 1, 3.778, 18, 3.922, -11, 4.067, -11, 1, 4.222, -11, 4.378, 11, 4.533, 11, 1, 4.767, 11, 5, 0, 5.233, 0, 1, 6.022, 0, 6.811, 0, 7.6, 0, 1, 7.733, 0, 7.867, -9.78, 8, -9.78, 0, 9.233, -9.78]}, {"Target": "Parameter", "Id": "ParamAngleY", "Segments": [0, 0, 1, 0.1, 0, 0.2, -0.929, 0.3, -2.583, 1, 0.522, -6.258, 0.744, -8, 0.967, -8, 1, 1.2, -8, 1.433, 29, 1.667, 29, 1, 1.789, 29, 1.911, 11, 2.033, 11, 1, 2.344, 11, 2.656, 25, 2.967, 25, 1, 3.067, 25, 3.167, -12, 3.267, -12, 1, 3.389, -12, 3.511, -7.897, 3.633, -7, 1, 3.778, -5.94, 3.922, -6, 4.067, -6, 1, 4.222, -6, 4.378, -12, 4.533, -12, 1, 4.733, -12, 4.933, 10.008, 5.133, 10.008, 1, 5.278, 10.008, 5.422, 0, 5.567, 0, 1, 5.811, 0, 6.056, 24.18, 6.3, 24.18, 1, 6.467, 24.18, 6.633, 24.212, 6.8, 22.08, 1, 6.911, 20.659, 7.022, -8.62, 7.133, -8.62, 1, 7.278, -8.62, 7.422, 2.9, 7.567, 2.9, 1, 7.733, 2.9, 7.9, -10.9, 8.067, -10.9, 1, 8.2, -10.9, 8.333, -0.64, 8.467, -0.64, 1, 8.722, -0.64, 8.978, -3.7, 9.233, -3.7]}, {"Target": "Parameter", "Id": "ParamAngleZ", "Segments": [0, 0, 1, 0.256, 0, 0.511, -7, 0.767, -7, 1, 0.856, -7, 0.944, 6.52, 1.033, 7, 1, 1.211, 7.959, 1.389, 8, 1.567, 8, 1, 1.711, 8, 1.856, -10, 2, -10, 1, 2.311, -10, 2.622, 2, 2.933, 2, 1, 3.133, 2, 3.333, 0, 3.533, 0, 1, 4.867, 0, 6.2, 0, 7.533, 0, 1, 7.678, 0, 7.822, 6, 7.967, 6, 1, 8.233, 6, 8.5, 4.08, 8.767, 4.08, 0, 9.233, 4.08]}, {"Target": "Parameter", "Id": "ParamCheek", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamFaceInkOn", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeLOpen", "Segments": [0, 1, 1, 0.267, 1, 0.533, 1, 0.8, 1, 1, 0.844, 1, 0.889, 0, 0.933, 0, 1, 1.544, 0, 2.156, 0, 2.767, 0, 1, 2.8, 0, 2.833, 1, 2.867, 1, 1, 3.522, 1, 4.178, 1, 4.833, 1, 1, 4.933, 1, 5.033, 0, 5.133, 0, 1, 5.744, 0, 6.356, 0, 6.967, 0, 1, 7.011, 0, 7.056, 1, 7.1, 1, 1, 7.344, 1, 7.589, 1, 7.833, 1, 1, 7.889, 1, 7.944, 0, 8, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeLSmile", "Segments": [0, 0, 1, 2.611, 0, 5.222, 0, 7.833, 0, 1, 7.889, 0, 7.944, 1, 8, 1, 0, 9.233, 1]}, {"Target": "Parameter", "Id": "ParamEyeLForm", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeROpen", "Segments": [0, 1, 1, 0.267, 1, 0.533, 1, 0.8, 1, 1, 0.844, 1, 0.889, 0, 0.933, 0, 1, 1.178, 0, 1.422, 0, 1.667, 0, 1, 1.711, 0, 1.756, 1, 1.8, 1, 1, 2, 1, 2.2, 1, 2.4, 1, 1, 2.433, 1, 2.467, 0, 2.5, 0, 1, 2.589, 0, 2.678, 0, 2.767, 0, 1, 2.8, 0, 2.833, 1, 2.867, 1, 1, 3.522, 1, 4.178, 1, 4.833, 1, 1, 4.933, 1, 5.033, 0, 5.133, 0, 1, 5.744, 0, 6.356, 0, 6.967, 0, 1, 7.011, 0, 7.056, 1, 7.1, 1, 1, 7.344, 1, 7.589, 1, 7.833, 1, 1, 7.889, 1, 7.944, 0, 8, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeRSmile", "Segments": [0, 0, 1, 2.611, 0, 5.222, 0, 7.833, 0, 1, 7.889, 0, 7.944, 1, 8, 1, 0, 9.233, 1]}, {"Target": "Parameter", "Id": "ParamEyeRForm", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeBallX", "Segments": [0, 0, 1, 0.533, 0, 1.067, 0, 1.6, 0, 1, 1.622, 0, 1.644, -0.96, 1.667, -0.96, 1, 1.8, -0.96, 1.933, -0.96, 2.067, -0.96, 1, 2.222, -0.96, 2.378, -0.2, 2.533, -0.2, 1, 2.567, -0.2, 2.6, -0.2, 2.633, -0.2, 1, 2.678, -0.2, 2.722, -0.047, 2.767, 0, 1, 3.044, 0.294, 3.322, 0.4, 3.6, 0.4, 1, 3.778, 0.4, 3.956, -0.4, 4.133, -0.4, 1, 4.3, -0.4, 4.467, 0.5, 4.633, 0.5, 1, 4.778, 0.5, 4.922, -0.5, 5.067, -0.5, 1, 5.211, -0.5, 5.356, 0, 5.5, 0, 1, 6.178, 0, 6.856, 0, 7.533, 0, 1, 7.644, 0, 7.756, -0.796, 7.867, -0.796, 0, 9.233, -0.796]}, {"Target": "Parameter", "Id": "ParamEyeBallY", "Segments": [0, 0, 1, 0.533, 0, 1.067, 0, 1.6, 0, 1, 1.622, 0, 1.644, 0.898, 1.667, 0.898, 1, 1.8, 0.898, 1.933, 0.898, 2.067, 0.898, 1, 2.222, 0.898, 2.378, 0.2, 2.533, 0.2, 1, 2.567, 0.2, 2.6, 0.2, 2.633, 0.2, 1, 2.678, 0.2, 2.722, 0, 2.767, 0, 1, 2.8, 0, 2.833, 0.5, 2.867, 0.5, 1, 3.111, 0.5, 3.356, -0.1, 3.6, -0.1, 1, 3.778, -0.1, 3.956, -0.1, 4.133, -0.1, 1, 4.3, -0.1, 4.467, -0.8, 4.633, -0.8, 1, 4.778, -0.8, 4.922, -0.8, 5.067, -0.8, 1, 5.211, -0.8, 5.356, 0.538, 5.5, 0.538, 1, 5.911, 0.538, 6.322, 0.5, 6.733, 0.5, 1, 6.856, 0.5, 6.978, 0.7, 7.1, 0.7, 0, 9.233, 0.7]}, {"Target": "Parameter", "Id": "ParamEyeBallForm", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamEyeEffect", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowLY", "Segments": [0, 0, 1, 0.911, 0, 1.822, 0, 2.733, 0, 1, 2.811, 0, 2.889, 0.6, 2.967, 0.6, 1, 3.589, 0.6, 4.211, 0.6, 4.833, 0.6, 1, 4.922, 0.6, 5.011, 0, 5.1, 0, 1, 5.656, 0, 6.211, 0, 6.767, 0, 1, 6.867, 0, 6.967, -0.3, 7.067, -0.3, 0, 9.233, -0.3]}, {"Target": "Parameter", "Id": "ParamBrowRY", "Segments": [0, 0, 1, 0.911, 0, 1.822, 0, 2.733, 0, 1, 2.811, 0, 2.889, 0.6, 2.967, 0.6, 1, 3.589, 0.6, 4.211, 0.6, 4.833, 0.6, 1, 4.922, 0.6, 5.011, 0, 5.1, 0, 1, 5.656, 0, 6.211, 0, 6.767, 0, 1, 6.867, 0, 6.967, -0.3, 7.067, -0.3, 0, 9.233, -0.3]}, {"Target": "Parameter", "Id": "ParamBrowLX", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowRX", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowLAngle", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowRAngle", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowLForm", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamBrowRForm", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamA", "Segments": [0, 0, 1, 1.222, 0, 2.444, 0, 3.667, 0, 1, 3.678, 0, 3.689, 1, 3.7, 1, 1, 4.778, 1, 5.856, 1, 6.933, 1, 1, 6.989, 1, 7.044, 0, 7.1, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamI", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamU", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamE", "Segments": [0, 0, 1, 1.056, 0, 2.111, 0, 3.167, 0, 1, 3.267, 0, 3.367, 1, 3.467, 1, 1, 4.011, 1, 4.556, 1, 5.1, 1, 1, 5.6, 1, 6.1, 0, 6.6, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamO", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamMouthUp", "Segments": [0, 1, 0, 9.233, 1]}, {"Target": "Parameter", "Id": "ParamMouthDown", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamMouthAngry", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamMouthAngryLine", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleX", "Segments": [0, 0, 1, 0.211, 0, 0.422, -3, 0.633, -3, 1, 0.889, -3, 1.144, -3, 1.4, -3, 1, 1.589, -3, 1.778, -4, 1.967, -4, 1, 2.311, -4, 2.656, 1, 3, 1, 1, 3.122, 1, 3.244, 0, 3.367, 0, 1, 3.489, 0, 3.611, 0, 3.733, 0, 1, 3.889, 0, 4.044, -1.992, 4.2, -1.992, 1, 4.333, -1.992, 4.467, 1, 4.6, 1, 1, 4.778, 1, 4.956, 0, 5.133, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamBodyAngleY", "Segments": [0, 0, 1, 0.1, 0, 0.2, -1, 0.3, -1, 1, 0.389, -1, 0.478, 0, 0.567, 0, 1, 0.611, 0, 0.656, -1, 0.7, -1, 1, 0.778, -1, 0.856, 8.17, 0.933, 9, 1, 1.033, 10, 1.133, 10, 1.233, 10, 1, 1.456, 10, 1.678, 5, 1.9, 5, 1, 2.1, 5, 2.3, 9, 2.5, 9, 1, 2.667, 9, 2.833, -4, 3, -4, 1, 3.144, -4, 3.289, 1.691, 3.433, 4, 1, 3.611, 6.842, 3.789, 7, 3.967, 7, 1, 4.289, 7, 4.611, 6.848, 4.933, 5, 1, 5.122, 3.917, 5.311, 0, 5.5, 0, 1, 5.756, 0, 6.011, 10, 6.267, 10, 1, 6.422, 10, 6.578, 10, 6.733, 9.22, 1, 6.833, 8.7, 6.933, -1, 7.033, -1, 1, 7.167, -1, 7.3, 2, 7.433, 2, 1, 7.6, 2, 7.767, -1, 7.933, -1, 1, 8.089, -1, 8.244, 0.56, 8.4, 0.56, 1, 8.644, 0.56, 8.889, -0.32, 9.133, -0.32, 0, 9.233, -0.32]}, {"Target": "Parameter", "Id": "ParamBodyAngleZ", "Segments": [0, 0, 1, 0.189, 0, 0.378, -1, 0.567, -1, 1, 0.689, -1, 0.811, 4.087, 0.933, 4.999, 1, 1.078, 6.077, 1.222, 5.999, 1.367, 5.999, 1, 1.533, 5.999, 1.7, 0, 1.867, 0, 1, 2.067, 0, 2.267, 0.102, 2.467, 1, 1, 2.556, 1.399, 2.644, 4, 2.733, 4, 1, 3.022, 4, 3.311, 0, 3.6, 0, 1, 4.889, 0, 6.178, 0, 7.467, 0, 1, 7.667, 0, 7.867, -1.46, 8.067, -1.46, 0, 9.233, -1.46]}, {"Target": "Parameter", "Id": "ParamBreath", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamLeftShoulderUp", "Segments": [0, 0, 1, 0.144, 0, 0.289, 0, 0.433, 0, 1, 0.578, 0, 0.722, -3, 0.867, -3, 1, 1, -3, 1.133, 0, 1.267, 0, 1, 1.656, 0, 2.044, -0.146, 2.433, -1.626, 1, 2.678, -2.556, 2.922, -5, 3.167, -5, 1, 3.422, -5, 3.678, -0.55, 3.933, 0.391, 1, 4.211, 1.414, 4.489, 1.31, 4.767, 1.31, 1, 5.033, 1.31, 5.3, -2.11, 5.567, -2.11, 1, 5.844, -2.11, 6.122, 4, 6.4, 4, 1, 6.567, 4, 6.733, 3.965, 6.9, 3.44, 1, 6.978, 3.195, 7.056, 0, 7.133, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamRightShoulderUp", "Segments": [0, 0, 1, 0.156, 0, 0.311, -10, 0.467, -10, 1, 0.678, -10, 0.889, -4.095, 1.1, 0.113, 1, 1.222, 2.549, 1.344, 2.402, 1.467, 2.402, 1, 1.644, 2.402, 1.822, 0, 2, 0, 1, 2.189, 0, 2.378, 0, 2.567, 0, 1, 2.778, 0, 2.989, -5.667, 3.2, -5.667, 1, 3.444, -5.667, 3.689, -0.723, 3.933, 0.177, 1, 4.211, 1.199, 4.489, 1.098, 4.767, 1.098, 1, 5.033, 1.098, 5.3, -2.32, 5.567, -2.32, 1, 5.844, -2.32, 6.122, 4, 6.4, 4, 1, 6.567, 4, 6.733, 3.959, 6.9, 3.38, 1, 6.978, 3.11, 7.056, 0, 7.133, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamArmLA01", "Segments": [0, 0, 1, 0.2, 0, 0.4, -3, 0.6, -3, 1, 0.844, -3, 1.089, 8, 1.333, 8, 1, 1.556, 8, 1.778, -4, 2, -4, 1, 2.2, -4, 2.4, 0, 2.6, 0, 1, 2.711, 0, 2.822, -5.91, 2.933, -5.91, 1, 3.078, -5.91, 3.222, -1.199, 3.367, 1, 1, 3.5, 3.03, 3.633, 3, 3.767, 3, 1, 4.022, 3, 4.278, 0.803, 4.533, 0.803, 1, 4.744, 0.803, 4.956, 0.759, 5.167, 1.28, 1, 5.433, 1.938, 5.7, 3.28, 5.967, 3.28, 1, 6.222, 3.28, 6.478, 3.247, 6.733, 2.8, 1, 6.844, 2.606, 6.956, -1, 7.067, -1, 1, 7.211, -1, 7.356, -0.4, 7.5, -0.4, 1, 7.678, -0.4, 7.856, -2, 8.033, -2, 1, 8.233, -2, 8.433, -1, 8.633, -1, 0, 9.233, -1]}, {"Target": "Parameter", "Id": "ParamArmLA02", "Segments": [0, 0, 1, 0.222, 0, 0.444, -5, 0.667, -5, 1, 0.944, -5, 1.222, -2, 1.5, -2, 1, 1.722, -2, 1.944, -3, 2.167, -3, 1, 2.344, -3, 2.522, -2, 2.7, -2, 1, 2.811, -2, 2.922, -3, 3.033, -3, 1, 3.3, -3, 3.567, 2, 3.833, 2, 1, 4.111, 2, 4.389, -0.5, 4.667, -0.5, 1, 4.856, -0.5, 5.044, -0.337, 5.233, 0, 1, 5.5, 0.476, 5.767, 0.72, 6.033, 0.72, 1, 6.278, 0.72, 6.522, 0.654, 6.767, 0.18, 1, 6.878, -0.036, 6.989, -0.9, 7.1, -0.9, 1, 7.244, -0.9, 7.389, -0.75, 7.533, -0.75, 1, 7.711, -0.75, 7.889, -1.62, 8.067, -1.62, 1, 8.267, -1.62, 8.467, -0.9, 8.667, -0.9, 0, 9.233, -0.9]}, {"Target": "Parameter", "Id": "ParamArmLA03", "Segments": [0, 0, 1, 0.233, 0, 0.467, -3, 0.7, -3, 1, 1.022, -3, 1.344, 5.5, 1.667, 5.5, 1, 1.833, 5.5, 2, 2.007, 2.167, 0.96, 1, 2.333, -0.087, 2.5, 0, 2.667, 0, 1, 2.922, 0, 3.178, 7, 3.433, 7, 1, 3.678, 7, 3.922, 5.5, 4.167, 5.5, 1, 4.544, 5.5, 4.922, 5.533, 5.3, 6, 1, 5.567, 6.329, 5.833, 7.14, 6.1, 7.14, 1, 6.333, 7.14, 6.567, 7.14, 6.8, 7.14, 1, 6.911, 7.14, 7.022, 0.96, 7.133, 0.96, 1, 7.278, 0.96, 7.422, 3.78, 7.567, 3.78, 0, 9.233, 3.78]}, {"Target": "Parameter", "Id": "ParamHandLA", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamArmRA01", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamArmRA02", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamArmRA03", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamWandRotate", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamHandRA", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamInkDrop", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamInkDropRotate", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamInkDropOn", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamArmLB01", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamArmLB02", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamArmLB03", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamHandLB", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamHatForm", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamArmRB01", "Segments": [0, 0, 1, 0.189, 0, 0.378, -20, 0.567, -20, 1, 0.711, -20, 0.856, -6.905, 1, 0, 1, 1.2, 9.56, 1.4, 11, 1.6, 11, 1, 1.689, 11, 1.778, -12, 1.867, -12, 1, 1.922, -12, 1.978, -10, 2.033, -10, 1, 2.244, -10, 2.456, -10, 2.667, -10, 1, 2.789, -10, 2.911, -16, 3.033, -16, 1, 3.278, -16, 3.522, -10, 3.767, -10, 1, 4.233, -10, 4.7, -10, 5.167, -10, 1, 5.433, -10, 5.7, -6.1, 5.967, -6.1, 1, 6.222, -6.1, 6.478, -6.228, 6.733, -7.48, 1, 6.844, -8.024, 6.956, -13.66, 7.067, -13.66, 1, 7.211, -13.66, 7.356, -12.76, 7.5, -12.76, 1, 7.678, -12.76, 7.856, -14.44, 8.033, -14.44, 1, 8.233, -14.44, 8.433, -13.48, 8.633, -13.48, 0, 9.233, -13.48]}, {"Target": "Parameter", "Id": "ParamArmRB02", "Segments": [0, 0, 1, 0.067, 0, 0.133, -0.261, 0.2, 1.053, 1, 0.289, 2.805, 0.378, 13.846, 0.467, 13.846, 1, 0.656, 13.846, 0.844, -18.096, 1.033, -20, 1, 1.256, -22.24, 1.478, -22, 1.7, -22, 1, 1.767, -22, 1.833, -2, 1.9, -2, 1, 2.122, -2, 2.344, -6, 2.567, -6, 1, 2.7, -6, 2.833, -6, 2.967, -6, 1, 3.256, -6, 3.544, -2, 3.833, -2, 1, 4.3, -2, 4.767, -2, 5.233, -2, 1, 5.5, -2, 5.767, -6.74, 6.033, -6.74, 1, 6.278, -6.74, 6.522, -6.674, 6.767, -5.9, 1, 6.878, -5.548, 6.989, -2.24, 7.1, -2.24, 1, 7.244, -2.24, 7.389, -2.96, 7.533, -2.96, 1, 7.711, -2.96, 7.889, -1.52, 8.067, -1.52, 1, 8.267, -1.52, 8.467, -2.3, 8.667, -2.3, 0, 9.233, -2.3]}, {"Target": "Parameter", "Id": "ParamArmRB02Y", "Segments": [0, 0, 1, 0.122, 0, 0.244, 23, 0.367, 23, 1, 0.644, 23, 0.922, 0, 1.2, 0, 1, 1.322, 0, 1.444, 0, 1.567, 0, 1, 1.678, 0, 1.789, 14.656, 1.9, 14.656, 1, 2, 14.656, 2.1, 4.615, 2.2, 4.615, 1, 2.444, 4.615, 2.689, 10.202, 2.933, 10.202, 1, 3.167, 10.202, 3.4, 0, 3.633, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamArmRB03", "Segments": [0, 0, 1, 0.144, 0, 0.289, -19.973, 0.433, -19.973, 1, 0.633, -19.973, 0.833, -3, 1.033, -3, 1, 1.233, -3, 1.433, -3, 1.633, -3, 1, 1.722, -3, 1.811, 11, 1.9, 11, 1, 2.222, 11, 2.544, 11, 2.867, 11, 1, 3.211, 11, 3.556, 20, 3.9, 20, 1, 4.367, 20, 4.833, 19.495, 5.3, 17.63, 1, 5.567, 16.564, 5.833, 14.233, 6.1, 12.95, 1, 6.333, 11.828, 6.567, 11.55, 6.8, 11.55, 1, 6.911, 11.55, 7.022, 16.03, 7.133, 16.03, 1, 7.278, 16.03, 7.422, 8, 7.567, 8, 0, 9.233, 8]}, {"Target": "Parameter", "Id": "ParamHandRB", "Segments": [0, 0, 1, 0.156, 0, 0.311, -10, 0.467, -10, 1, 0.667, -10, 0.867, -10, 1.067, -10, 1, 1.256, -10, 1.444, 10, 1.633, 10, 1, 1.733, 10, 1.833, -10, 1.933, -10, 1, 2.222, -10, 2.511, -10, 2.8, -10, 1, 3.267, -10, 3.733, 0, 4.2, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamAllX", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamAllY", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamAllRotate", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamHairFront", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamHairSideL", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamHairSideR", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamHairBack", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamHairBackR", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamHairBackL", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamHairFrontFuwa", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamHairSideFuwa", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamHairBackFuwa", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamWing", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamRibbon", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamHatBrim", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamHatTop", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamAccessory1", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamAccessory2", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamString", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamRobeL", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamRobeR", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamRobeFuwa", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamHeartMissOn", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamHeartBackMissOn", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamHeartColorRainbow", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamHeartHealOn", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamHeartBackHealOn", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamHeartColorHeal", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamHeartDrow", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamHeartSize", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamHeartColorLight", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamWandInkColorRainbow", "Segments": [0, 0, 1, 0.544, 0, 1.089, 0, 1.633, 0, 1, 1.7, 0, 1.767, 9.93, 1.833, 9.93, 1, 3.622, 9.93, 5.411, 9.93, 7.2, 9.93, 1, 7.311, 9.93, 7.422, 0, 7.533, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamWandInkColorHeal", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamWandInk", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamSmokeOn", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamSmoke", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamExplosionChargeOn", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamExplosionLightCharge", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamExplosionOn", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamExplosion", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamRabbitX", "Segments": [0, 0, 1, 0.244, 0, 0.489, 0, 0.733, 0, 1, 1.078, 0, 1.422, 0.179, 1.767, 0.179, 1, 1.811, 0.179, 1.856, 0.072, 1.9, 0.035, 1, 1.933, 0.007, 1.967, 0.012, 2, 0.012, 1, 2.078, 0.012, 2.156, 0.022, 2.233, 0.035, 1, 2.322, 0.049, 2.411, 0.054, 2.5, 0.054, 1, 2.578, 0.054, 2.656, 0.053, 2.733, 0.046, 1, 2.789, 0.04, 2.844, 0.022, 2.9, 0.012, 1, 2.956, 0.003, 3.011, 0, 3.067, 0, 1, 3.156, 0, 3.244, 0, 3.333, 0.55, 1, 3.411, 1, 3.489, 1, 3.567, 1, 1, 3.656, 1, 3.744, 0.896, 3.833, 0.55, 1, 3.911, 0.247, 3.989, 0, 4.067, 0, 1, 4.156, 0, 4.244, 0, 4.333, 0.55, 1, 4.411, 1, 4.489, 1, 4.567, 1, 1, 4.656, 1, 4.744, 0.896, 4.833, 0.55, 1, 4.911, 0.247, 4.989, 0, 5.067, 0, 1, 5.156, 0, 5.244, 0, 5.333, 0.55, 1, 5.411, 1, 5.489, 1, 5.567, 1, 1, 5.656, 1, 5.744, 0.896, 5.833, 0.55, 1, 5.911, 0.247, 5.989, 0, 6.067, 0, 1, 6.156, 0, 6.244, 0, 6.333, 0.55, 1, 6.411, 1, 6.489, 1, 6.567, 1, 1, 6.656, 1, 6.744, 0.896, 6.833, 0.55, 1, 6.911, 0.247, 6.989, 0, 7.067, 0, 1, 7.133, 0, 7.2, 0.013, 7.267, 0.032, 1, 7.322, 0.048, 7.378, 0.053, 7.433, 0.053, 1, 7.522, 0.053, 7.611, 0.028, 7.7, 0.014, 1, 7.789, 0, 7.878, 0, 7.967, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamRabbitY", "Segments": [0, 0.94, 1, 0.589, 0.94, 1.178, 0.94, 1.767, 0.94, 1, 1.811, 0.94, 1.856, 0.908, 1.9, 0.908, 1, 1.933, 0.908, 1.967, 0.914, 2, 0.924, 1, 2.056, 0.942, 2.111, 0.95, 2.167, 0.95, 1, 2.3, 0.95, 2.433, 0.95, 2.567, 0.944, 1, 2.733, 0.937, 2.9, 0.951, 3.067, 0.91, 1, 3.644, 0.767, 4.222, 0, 4.8, 0, 1, 5.389, 0, 5.978, 0.94, 6.567, 0.94, 1, 6.722, 0.94, 6.878, 0.94, 7.033, 0.94, 1, 7.111, 0.94, 7.189, 0.929, 7.267, 0.929, 1, 7.356, 0.929, 7.444, 0.943, 7.533, 0.943, 1, 7.667, 0.943, 7.8, 0.934, 7.933, 0.934, 0, 9.233, 0.934]}, {"Target": "Parameter", "Id": "ParamRabbitRotate", "Segments": [0, 0, 1, 0.278, 0, 0.556, 0, 0.833, 0, 1, 1.1, 0, 1.367, -0.002, 1.633, -0.037, 1, 1.789, -0.057, 1.944, -0.135, 2.1, -0.135, 1, 2.2, -0.135, 2.3, 0.182, 2.4, 0.19, 1, 2.589, 0.205, 2.778, 0.202, 2.967, 0.22, 1, 3.133, 0.236, 3.3, 0.4, 3.467, 0.4, 1, 3.633, 0.4, 3.8, -0.4, 3.967, -0.4, 1, 4.133, -0.4, 4.3, 0.4, 4.467, 0.4, 1, 4.633, 0.4, 4.8, -0.4, 4.967, -0.4, 1, 5.133, -0.4, 5.3, -0.4, 5.467, -0.4, 1, 5.633, -0.4, 5.8, 0.4, 5.967, 0.4, 1, 6.133, 0.4, 6.3, -0.4, 6.467, -0.4, 1, 6.633, -0.4, 6.8, 0.2, 6.967, 0.2, 1, 7.067, 0.2, 7.167, 0, 7.267, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamRabbitEliminationEffect", "Segments": [0, 0, 1, 0.322, 0, 0.644, 0, 0.967, 0, 1, 1.167, 0, 1.367, 30, 1.567, 30, 1, 3.778, 30, 5.989, 30, 8.2, 30, 1, 8.4, 30, 8.6, 0, 8.8, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamRabbitAppearance", "Segments": [0, 0, 1, 0.589, 0, 1.178, 0, 1.767, 0, 1, 1.922, 0, 2.078, 28.123, 2.233, 28.123, 1, 2.278, 28.123, 2.322, 27.654, 2.367, 27.654, 1, 2.411, 27.654, 2.456, 28.949, 2.5, 29.309, 1, 2.578, 29.939, 2.656, 30, 2.733, 30, 1, 4.411, 30, 6.089, 30, 7.767, 30, 1, 7.967, 30, 8.167, 0, 8.367, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamRabbitSize", "Segments": [0, 0, 1, 0.644, 0, 1.289, 0, 1.933, 0, 1, 1.978, 0, 2.022, 0.4, 2.067, 0.4, 1, 2.111, 0.4, 2.156, 0, 2.2, 0, 1, 2.489, 0, 2.778, 0, 3.067, 0, 1, 3.156, 0, 3.244, 0.8, 3.333, 0.8, 1, 3.411, 0.8, 3.489, 0.496, 3.567, 0, 1, 3.656, -0.566, 3.744, -0.8, 3.833, -0.8, 1, 3.911, -0.8, 3.989, -0.496, 4.067, 0, 1, 4.156, 0.566, 4.244, 0.8, 4.333, 0.8, 1, 4.411, 0.8, 4.489, 0.496, 4.567, 0, 1, 4.656, -0.566, 4.744, -0.8, 4.833, -0.8, 1, 4.911, -0.8, 4.989, -0.529, 5.067, 0, 1, 5.156, 0.604, 5.244, 0.9, 5.333, 0.9, 1, 5.411, 0.9, 5.489, 0.521, 5.567, 0, 1, 5.656, -0.595, 5.744, -0.8, 5.833, -0.8, 1, 5.911, -0.8, 5.989, -0.496, 6.067, 0, 1, 6.156, 0.566, 6.244, 0.8, 6.333, 0.8, 1, 6.411, 0.8, 6.489, 0.496, 6.567, 0, 1, 6.656, -0.566, 6.744, -0.8, 6.833, -0.8, 1, 6.911, -0.8, 6.989, 0, 7.067, 0, 1, 7.3, 0, 7.533, 0, 7.767, 0, 1, 7.811, 0, 7.856, 0.4, 7.9, 0.4, 1, 7.944, 0.4, 7.989, -1, 8.033, -1, 0, 9.233, -1]}, {"Target": "Parameter", "Id": "ParamRabbitDraworder", "Segments": [0, 0, 1, 1.189, 0, 2.378, 0, 3.567, 0, 1, 3.578, 0, 3.589, -1, 3.6, -1, 1, 3.8, -1, 4, -1, 4.2, -1, 1, 4.211, -1, 4.222, 0, 4.233, 0, 1, 4.344, 0, 4.456, 0, 4.567, 0, 1, 4.578, 0, 4.589, -1, 4.6, -1, 1, 4.756, -1, 4.911, -1, 5.067, -1, 1, 5.078, -1, 5.089, 0, 5.1, 0, 1, 5.256, 0, 5.411, 0, 5.567, 0, 1, 5.578, 0, 5.589, -1, 5.6, -1, 1, 5.756, -1, 5.911, -1, 6.067, -1, 1, 6.078, -1, 6.089, 0, 6.1, 0, 1, 6.256, 0, 6.411, 0, 6.567, 0, 1, 6.578, 0, 6.589, -1, 6.6, -1, 0, 9.233, -1]}, {"Target": "Parameter", "Id": "ParamRabbitEar", "Segments": [0, 0, 1, 0.322, 0, 0.644, 0, 0.967, 0, 1, 1.267, 0, 1.567, 0.559, 1.867, 0.559, 1, 1.978, 0.559, 2.089, -0.597, 2.2, -0.597, 1, 2.333, -0.597, 2.467, -0.317, 2.6, -0.134, 1, 2.7, 0.004, 2.8, 0, 2.9, 0, 1, 3.067, 0, 3.233, -1, 3.4, -1, 1, 3.567, -1, 3.733, 1, 3.9, 1, 1, 4.067, 1, 4.233, -1, 4.4, -1, 1, 4.567, -1, 4.733, 1, 4.9, 1, 1, 5.067, 1, 5.233, -1, 5.4, -1, 1, 5.567, -1, 5.733, 1, 5.9, 1, 1, 6.067, 1, 6.233, -1, 6.4, -1, 1, 6.567, -1, 6.733, 1, 6.9, 1, 1, 7.022, 1, 7.144, -0.5, 7.267, -0.5, 1, 7.344, -0.5, 7.422, 0.559, 7.5, 0.559, 1, 7.544, 0.559, 7.589, 0.186, 7.633, 0.024, 1, 7.678, -0.138, 7.722, -0.134, 7.767, -0.134, 1, 7.822, -0.134, 7.878, 0, 7.933, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "Param5", "Segments": [0, 0, 1, 0.589, 0, 1.178, 0, 1.767, 0, 1, 1.9, 0, 2.033, 0.5, 2.167, 0.5, 1, 2.344, 0.5, 2.522, 0.5, 2.7, 0.5, 1, 2.822, 0.5, 2.944, 1, 3.067, 1, 1, 3.211, 1, 3.356, 1, 3.5, 1, 1, 3.544, 1, 3.589, 0, 3.633, 0, 1, 3.756, 0, 3.878, 0, 4, 0, 1, 4.044, 0, 4.089, 1, 4.133, 1, 1, 4.256, 1, 4.378, 1, 4.5, 1, 1, 4.544, 1, 4.589, 0, 4.633, 0, 1, 4.756, 0, 4.878, 0, 5, 0, 1, 5.044, 0, 5.089, 1, 5.133, 1, 1, 5.256, 1, 5.378, 1, 5.5, 1, 1, 5.544, 1, 5.589, 0, 5.633, 0, 1, 5.756, 0, 5.878, 0, 6, 0, 1, 6.044, 0, 6.089, 1, 6.133, 1, 1, 6.256, 1, 6.378, 1, 6.5, 1, 1, 6.544, 1, 6.589, 0, 6.633, 0, 1, 6.756, 0, 6.878, 0, 7, 0, 1, 7.1, 0, 7.2, 1, 7.3, 1, 0, 9.233, 1]}, {"Target": "Parameter", "Id": "ParamRabbitLight", "Segments": [0, 0, 1, 1.022, 0, 2.044, 0, 3.067, 0, 1, 3.144, 0, 3.222, 1, 3.3, 1, 1, 4.533, 1, 5.767, 1, 7, 1, 1, 7.089, 1, 7.178, 0, 7.267, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamRabbitLightSize", "Segments": [0, 0, 1, 1.022, 0, 2.044, 0, 3.067, 0, 1, 3.233, 0, 3.4, 1, 3.567, 1, 1, 3.733, 1, 3.9, -1, 4.067, -1, 1, 4.233, -1, 4.4, 1, 4.567, 1, 1, 4.733, 1, 4.9, -1, 5.067, -1, 1, 5.233, -1, 5.4, 1, 5.567, 1, 1, 5.733, 1, 5.9, -1, 6.067, -1, 1, 6.233, -1, 6.4, 1, 6.567, 1, 1, 6.733, 1, 6.9, 0, 7.067, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamAuraOn", "Segments": [0, 0, 1, 1.233, 0, 2.467, 0, 3.7, 0, 1, 3.867, 0.957, 4.033, 1, 4.2, 1, 1, 5, 1, 5.8, 1, 6.6, 1, 1, 6.956, 0.996, 7.311, 0.638, 7.667, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamAura", "Segments": [0, 0, 1, 1.167, 0, 2.333, 0, 3.5, 0, 0, 4.833, 29.268, 2, 4.867, 0, 0, 6.233, 29.268, 2, 6.267, 0, 0, 7.633, 29.268, 2, 7.667, 0, 0, 9.033, 29.268, 0, 9.233, 29.268]}, {"Target": "Parameter", "Id": "ParamAuraColor1", "Segments": [0, 0.5, 0, 9.233, 0.5]}, {"Target": "Parameter", "Id": "ParamAuraColor2", "Segments": [0, 0, 1, 0.9, 0, 1.8, 0, 2.7, 0, 1, 3.6, 0, 4.5, 1, 5.4, 1, 0, 9.233, 1]}, {"Target": "Parameter", "Id": "ParamHeartLightOn", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamHeartLight", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamHeartLightColor", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamHealLightOn", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamHealLight", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamStrengthenLightOn", "Segments": [0, 0, 1, 1.033, 0, 2.067, 0, 3.1, 0, 1, 3.456, 0, 3.811, 1, 4.167, 1, 1, 4.778, 1, 5.389, 1, 6, 1, 1, 6.356, 1, 6.711, 0, 7.067, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamStrengthenLight", "Segments": [0, 0, 1, 0.956, 0, 1.911, 0, 2.867, 0, 1, 2.911, 0, 2.956, 0.8, 3, 0.8, 1, 3.044, 0.8, 3.089, 0, 3.133, 0, 1, 3.178, 0, 3.222, 0.8, 3.267, 0.8, 1, 3.311, 0.8, 3.356, 0, 3.4, 0, 1, 3.444, 0, 3.489, 0.8, 3.533, 0.8, 1, 3.578, 0.8, 3.622, 0, 3.667, 0, 1, 3.711, 0, 3.756, 0.8, 3.8, 0.8, 1, 3.844, 0.8, 3.889, 0, 3.933, 0, 1, 3.978, 0, 4.022, 0.8, 4.067, 0.8, 1, 4.111, 0.8, 4.156, 0, 4.2, 0, 1, 4.244, 0, 4.289, 0.8, 4.333, 0.8, 1, 4.378, 0.8, 4.422, 0, 4.467, 0, 1, 4.511, 0, 4.556, 0.8, 4.6, 0.8, 1, 4.644, 0.8, 4.689, 0, 4.733, 0, 1, 4.778, 0, 4.822, 0.8, 4.867, 0.8, 1, 4.911, 0.8, 4.956, 0, 5, 0, 1, 5.044, 0, 5.089, 0.8, 5.133, 0.8, 1, 5.178, 0.8, 5.222, 0, 5.267, 0, 1, 5.311, 0, 5.356, 0.8, 5.4, 0.8, 1, 5.444, 0.8, 5.489, 0, 5.533, 0, 1, 5.578, 0, 5.622, 0.8, 5.667, 0.8, 1, 5.711, 0.8, 5.756, 0, 5.8, 0, 1, 5.844, 0, 5.889, 0.8, 5.933, 0.8, 1, 5.978, 0.8, 6.022, 0, 6.067, 0, 1, 6.111, 0, 6.156, 0.8, 6.2, 0.8, 1, 6.244, 0.8, 6.289, 0, 6.333, 0, 1, 6.378, 0, 6.422, 0.8, 6.467, 0.8, 1, 6.511, 0.8, 6.556, 0, 6.6, 0, 1, 6.644, 0, 6.689, 0.8, 6.733, 0.8, 1, 6.778, 0.8, 6.822, 0, 6.867, 0, 1, 6.911, 0, 6.956, 0.8, 7, 0.8, 1, 7.044, 0.8, 7.089, 0, 7.133, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamStrengthenLightMove", "Segments": [0, 0, 1, 1.033, 0, 2.067, 0, 3.1, 0, 1, 4.422, 0.731, 5.744, 1, 7.067, 1, 0, 9.233, 1]}, {"Target": "Parameter", "Id": "ParamMagicPositionX", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamMagicPositionY", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamAllColor1", "Segments": [0, 0, 0, 9.233, 0]}, {"Target": "Parameter", "Id": "ParamAllColor2", "Segments": [0, 0, 1, 1.022, 0, 2.044, 0, 3.067, 0, 1, 3.233, 0, 3.4, 0.85, 3.567, 0.85, 1, 3.733, 0.85, 3.9, 0.7, 4.067, 0.7, 1, 4.233, 0.7, 4.4, 0.85, 4.567, 0.85, 1, 4.733, 0.85, 4.9, 0.7, 5.067, 0.7, 1, 5.233, 0.7, 5.4, 0.85, 5.567, 0.85, 1, 5.733, 0.85, 5.9, 0.7, 6.067, 0.7, 1, 6.233, 0.7, 6.4, 0.85, 6.567, 0.85, 1, 6.733, 0.85, 6.9, 0, 7.067, 0, 0, 9.233, 0]}, {"Target": "PartOpacity", "Id": "PartArmRA", "Segments": [0, 0, 0, 9.23, 0]}, {"Target": "PartOpacity", "Id": "PartArmLB", "Segments": [0, 0, 0, 9.23, 0]}, {"Target": "PartOpacity", "Id": "PartArmRB", "Segments": [0, 1, 0, 9.23, 1]}, {"Target": "PartOpacity", "Id": "PartSketch", "Segments": [0, 0, 0, 9.23, 0]}]}