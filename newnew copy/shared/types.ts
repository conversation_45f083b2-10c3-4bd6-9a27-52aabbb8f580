// Shared TypeScript types for frontend and backend communication

export interface Message {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  conversationId: string;
  metadata?: {
    model?: string;
    provider?: string;
    audioUrl?: string;
    imageUrl?: string;
    processing?: boolean;
    audioTranscription?: boolean;
  };
}

export interface Conversation {
  id: string;
  title: string;
  createdAt: string;
  updatedAt: string;
  messages: Message[];
  metadata?: {
    model?: string;
    provider?: string;
  };
}

export interface Model {
  name: string;
  displayName: string;
  provider: string;
  maxTokens: number;
  temperature: number;
  available: boolean;
}

export interface Provider {
  name: string;
  displayName: string;
  models: Model[];
  available: boolean;
}

export interface WebSocketMessage {
  type: 'chat' | 'audio' | 'config' | 'ping' | 'chat_chunk' | 'chat_complete' | 'transcription' | 'typing' | 'audio_processing' | 'config_response' | 'error' | 'pong';
  [key: string]: any;
}

export interface ChatMessage extends WebSocketMessage {
  type: 'chat';
  content: string;
  conversationId: string;
  model: string;
  provider: string;
  images?: string[];
}

export interface AudioMessage extends WebSocketMessage {
  type: 'audio';
  audioData: string;
  format: string;
  conversationId: string;
  model: string;
  provider: string;
}

export interface ConfigMessage extends WebSocketMessage {
  type: 'config';
  configType: 'get_models' | 'test_connection';
  provider?: string;
  model?: string;
}

export interface ChatChunkMessage extends WebSocketMessage {
  type: 'chat_chunk';
  content: string;
  conversationId: string;
}

export interface ChatCompleteMessage extends WebSocketMessage {
  type: 'chat_complete';
  conversationId: string;
}

export interface TranscriptionMessage extends WebSocketMessage {
  type: 'transcription';
  text: string;
}

export interface TypingMessage extends WebSocketMessage {
  type: 'typing';
  status: boolean;
}

export interface AudioProcessingMessage extends WebSocketMessage {
  type: 'audio_processing';
  status: boolean;
}

export interface ConfigResponseMessage extends WebSocketMessage {
  type: 'config_response';
  configType: string;
  data: any;
}

export interface ErrorMessage extends WebSocketMessage {
  type: 'error';
  message: string;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ConversationResponse {
  id: number;
  title: string;
  createdAt: string;
  updatedAt: string;
  metadata: Record<string, any>;
}

export interface MessageResponse {
  id: number;
  conversationId: number;
  role: string;
  content: string;
  contentType: string;
  metadata: Record<string, any>;
  createdAt: string;
  audioUrl?: string;
  audioDuration?: number;
  imageUrl?: string;
  imageCaption?: string;
}

export interface FileResponse {
  id: number;
  filename: string;
  fileType: string;
  fileSize: number;
  processed: boolean;
  createdAt: string;
}

export interface DocumentResponse extends FileResponse {
  metadata: Record<string, any>;
}

// Configuration types
export interface ServerConfig {
  host: string;
  port: number;
  debug: boolean;
}

export interface LLMConfig {
  defaultProvider: string;
  providers: Record<string, {
    models: Array<{
      name: string;
      displayName: string;
      maxTokens: number;
      temperature: number;
    }>;
  }>;
}

export interface ASRConfig {
  defaultEngine: string;
  engines: string[];
}

export interface TTSConfig {
  defaultEngine: string;
  engines: string[];
}

export interface VADConfig {
  engine: string;
  threshold: number;
}

export interface RAGConfig {
  enabled: boolean;
  vectorStore: string;
  embeddingModel: string;
  chunkSize: number;
  maxResults: number;
}

export interface AudioConfig {
  sampleRate: number;
  channels: number;
  format: string;
}

export interface UploadsConfig {
  maxFileSize: number;
  allowedExtensions: string[];
}

export interface ConfigResponse {
  server: ServerConfig;
  llm: LLMConfig;
  asr: ASRConfig;
  tts: TTSConfig;
  vad: VADConfig;
  rag: RAGConfig;
  audio: AudioConfig;
  uploads: UploadsConfig;
}

// Settings types
export interface UserSettings {
  audioEnabled: boolean;
  autoPlayAudio: boolean;
  voiceId: string;
  temperature: number;
  maxTokens: number;
  theme: 'light' | 'dark' | 'auto';
  language: string;
  notifications: boolean;
  soundEffects: boolean;
}

// File upload types
export interface FileUpload {
  file: File;
  preview?: string;
  type: 'image' | 'document' | 'audio';
  status: 'pending' | 'uploading' | 'uploaded' | 'error';
  progress?: number;
  error?: string;
}

// Audio types
export interface AudioRecording {
  blob: Blob;
  duration: number;
  url: string;
}

export interface AudioVisualization {
  frequencies: number[];
  volume: number;
  isRecording: boolean;
}

// Search types
export interface SearchResult {
  content: string;
  metadata: Record<string, any>;
  score: number;
  rank: number;
}

export interface SearchResponse {
  results: SearchResult[];
}

// Error types
export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: string;
}

// Utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

// Event types
export interface AppEvent {
  type: string;
  payload?: any;
  timestamp: string;
}

export interface ConversationEvent extends AppEvent {
  type: 'conversation_created' | 'conversation_updated' | 'conversation_deleted';
  payload: {
    conversationId: string;
    conversation?: Conversation;
  };
}

export interface MessageEvent extends AppEvent {
  type: 'message_added' | 'message_updated' | 'message_deleted';
  payload: {
    messageId: string;
    conversationId: string;
    message?: Message;
  };
}

export interface ConnectionEvent extends AppEvent {
  type: 'connected' | 'disconnected' | 'reconnecting' | 'error';
  payload?: {
    error?: string;
    retryCount?: number;
  };
}
