#!/bin/bash

# Advanced Multimodal Chatbot Setup Script
# This script sets up both backend and frontend components

set -e  # Exit on any error

echo "🚀 Setting up Advanced Multimodal Chatbot..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required tools are installed
check_requirements() {
    print_status "Checking requirements..."
    
    # Check Python
    if ! command -v python3 &> /dev/null; then
        print_error "Python 3 is not installed. Please install Python 3.11 or later."
        exit 1
    fi
    
    python_version=$(python3 --version | cut -d' ' -f2)
    print_success "Python $python_version found"
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18 or later."
        exit 1
    fi
    
    node_version=$(node --version)
    print_success "Node.js $node_version found"
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm."
        exit 1
    fi
    
    npm_version=$(npm --version)
    print_success "npm $npm_version found"
    
    # Check uv (Python package manager)
    if ! command -v uv &> /dev/null; then
        print_warning "uv is not installed. Installing uv..."
        curl -LsSf https://astral.sh/uv/install.sh | sh
        source $HOME/.cargo/env
        
        if ! command -v uv &> /dev/null; then
            print_error "Failed to install uv. Please install it manually: https://github.com/astral-sh/uv"
            exit 1
        fi
    fi
    
    uv_version=$(uv --version)
    print_success "uv $uv_version found"
}

# Setup backend
setup_backend() {
    print_status "Setting up backend..."
    
    cd backend
    
    # Create virtual environment and install dependencies
    print_status "Installing Python dependencies..."
    uv sync
    
    # Copy environment file
    if [ ! -f .env ]; then
        print_status "Creating .env file..."
        cp .env.example .env
        print_warning "Please edit backend/.env file with your API keys and configuration"
    fi
    
    # Create necessary directories
    mkdir -p data/uploads
    mkdir -p data/chromadb
    mkdir -p logs
    
    print_success "Backend setup completed"
    cd ..
}

# Setup frontend
setup_frontend() {
    print_status "Setting up frontend..."
    
    cd frontend
    
    # Install dependencies
    print_status "Installing Node.js dependencies..."
    npm install
    
    # Install electron-is-dev if not present
    if ! npm list electron-is-dev &> /dev/null; then
        npm install electron-is-dev
    fi
    
    print_success "Frontend setup completed"
    cd ..
}

# Setup Ollama (optional)
setup_ollama() {
    print_status "Checking for Ollama..."
    
    if ! command -v ollama &> /dev/null; then
        print_warning "Ollama is not installed."
        echo "Would you like to install Ollama for local LLM support? (y/n)"
        read -r response
        
        if [[ "$response" =~ ^[Yy]$ ]]; then
            print_status "Installing Ollama..."
            
            # Install Ollama based on OS
            if [[ "$OSTYPE" == "linux-gnu"* ]]; then
                curl -fsSL https://ollama.ai/install.sh | sh
            elif [[ "$OSTYPE" == "darwin"* ]]; then
                print_status "Please download Ollama from https://ollama.ai/download/mac"
                print_status "Or install via Homebrew: brew install ollama"
            else
                print_status "Please download Ollama from https://ollama.ai/download"
            fi
        fi
    else
        ollama_version=$(ollama --version)
        print_success "Ollama $ollama_version found"
        
        # Pull a default model
        print_status "Pulling default model (llama3.1:latest)..."
        ollama pull llama3.1:latest || print_warning "Failed to pull model. You can do this later with: ollama pull llama3.1:latest"
    fi
}

# Create startup scripts
create_scripts() {
    print_status "Creating startup scripts..."
    
    # Backend startup script
    cat > start-backend.sh << 'EOF'
#!/bin/bash
echo "Starting chatbot backend..."
cd backend
uv run uvicorn src.main:app --reload --host localhost --port 8000
EOF
    chmod +x start-backend.sh
    
    # Frontend startup script
    cat > start-frontend.sh << 'EOF'
#!/bin/bash
echo "Starting chatbot frontend..."
cd frontend
npm start
EOF
    chmod +x start-frontend.sh
    
    # Electron startup script
    cat > start-electron.sh << 'EOF'
#!/bin/bash
echo "Starting chatbot Electron app..."
cd frontend
npm run electron-dev
EOF
    chmod +x start-electron.sh
    
    # Combined startup script
    cat > start-all.sh << 'EOF'
#!/bin/bash
echo "Starting Advanced Multimodal Chatbot..."

# Function to cleanup background processes
cleanup() {
    echo "Shutting down..."
    kill $(jobs -p) 2>/dev/null
    exit 0
}

# Set trap to cleanup on exit
trap cleanup SIGINT SIGTERM

# Start backend
echo "Starting backend..."
cd backend
uv run uvicorn src.main:app --reload --host localhost --port 8000 &
BACKEND_PID=$!
cd ..

# Wait for backend to start
echo "Waiting for backend to start..."
sleep 5

# Start frontend
echo "Starting frontend..."
cd frontend
npm start &
FRONTEND_PID=$!
cd ..

echo "✅ Chatbot is starting up!"
echo "📱 Web interface: http://localhost:3000"
echo "🔧 API docs: http://localhost:8000/docs"
echo "Press Ctrl+C to stop all services"

# Wait for processes
wait
EOF
    chmod +x start-all.sh
    
    print_success "Startup scripts created"
}

# Main setup function
main() {
    echo "🤖 Advanced Multimodal Chatbot Setup"
    echo "===================================="
    
    check_requirements
    setup_backend
    setup_frontend
    setup_ollama
    create_scripts
    
    print_success "Setup completed successfully! 🎉"
    echo ""
    echo "📋 Next steps:"
    echo "1. Edit backend/.env with your API keys"
    echo "2. Start the application:"
    echo "   • All services: ./start-all.sh"
    echo "   • Backend only: ./start-backend.sh"
    echo "   • Frontend only: ./start-frontend.sh"
    echo "   • Electron app: ./start-electron.sh"
    echo ""
    echo "🌐 Access points:"
    echo "   • Web interface: http://localhost:3000"
    echo "   • API documentation: http://localhost:8000/docs"
    echo "   • API health check: http://localhost:8000/health"
    echo ""
    echo "📚 For more information, see README.md"
}

# Run main function
main "$@"
