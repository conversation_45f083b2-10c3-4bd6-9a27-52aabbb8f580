"""Configuration management for the chatbot backend."""

import os
from pathlib import Path
from typing import Any, Dict, List, Optional

import yaml
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings


class ServerConfig(BaseModel):
    """Server configuration."""
    host: str = "localhost"
    port: int = 8000
    cors_origins: List[str] = ["http://localhost:3000"]
    debug: bool = True


class DatabaseConfig(BaseModel):
    """Database configuration."""
    url: str = "sqlite:///./chatbot.db"


class LLMModelConfig(BaseModel):
    """LLM model configuration."""
    name: str
    display_name: str
    max_tokens: int = 4096
    temperature: float = 0.7


class LLMProviderConfig(BaseModel):
    """LLM provider configuration."""
    base_url: Optional[str] = None
    api_key: Optional[str] = None
    models: List[LLMModelConfig] = []


class LLMConfig(BaseModel):
    """LLM configuration."""
    default_provider: str = "ollama"
    providers: Dict[str, LLMProviderConfig] = {}


class ASREngineConfig(BaseModel):
    """ASR engine configuration."""
    model: Optional[str] = None
    device: str = "cpu"
    language: str = "auto"
    api_key: Optional[str] = None
    region: Optional[str] = None


class ASRConfig(BaseModel):
    """ASR configuration."""
    default_engine: str = "whisper_local"
    engines: Dict[str, ASREngineConfig] = {}


class TTSEngineConfig(BaseModel):
    """TTS engine configuration."""
    voice: Optional[str] = None
    rate: str = "+0%"
    pitch: str = "+0Hz"
    model: Optional[str] = None
    api_key: Optional[str] = None
    region: Optional[str] = None


class TTSConfig(BaseModel):
    """TTS configuration."""
    default_engine: str = "edge_tts"
    engines: Dict[str, TTSEngineConfig] = {}


class VADConfig(BaseModel):
    """VAD configuration."""
    engine: str = "silero"
    threshold: float = 0.5
    min_speech_duration: float = 0.25
    min_silence_duration: float = 0.5


class ChromaDBConfig(BaseModel):
    """ChromaDB configuration."""
    persist_directory: str = "./data/chromadb"
    collection_name: str = "documents"


class RAGConfig(BaseModel):
    """RAG configuration."""
    enabled: bool = True
    vector_store: str = "chromadb"
    embedding_model: str = "sentence-transformers/all-MiniLM-L6-v2"
    chunk_size: int = 1000
    chunk_overlap: int = 200
    max_results: int = 5
    chromadb: ChromaDBConfig = ChromaDBConfig()


class AudioConfig(BaseModel):
    """Audio configuration."""
    sample_rate: int = 16000
    channels: int = 1
    chunk_size: int = 1024
    format: str = "wav"


class UploadsConfig(BaseModel):
    """File uploads configuration."""
    max_file_size: int = 10485760  # 10MB
    allowed_extensions: List[str] = [".txt", ".pdf", ".docx", ".png", ".jpg", ".jpeg", ".gif", ".webp"]
    upload_directory: str = "./data/uploads"


class LoggingConfig(BaseModel):
    """Logging configuration."""
    level: str = "INFO"
    format: str = "{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} | {message}"
    file: str = "./logs/chatbot.log"
    rotation: str = "10 MB"
    retention: str = "30 days"


class RedisConfig(BaseModel):
    """Redis configuration."""
    url: str = "redis://localhost:6379/0"


class SecurityConfig(BaseModel):
    """Security configuration."""
    secret_key: str
    algorithm: str = "HS256"
    access_token_expire_minutes: int = 30


class Config(BaseModel):
    """Main configuration."""
    server: ServerConfig = ServerConfig()
    database: DatabaseConfig = DatabaseConfig()
    llm: LLMConfig = LLMConfig()
    asr: ASRConfig = ASRConfig()
    tts: TTSConfig = TTSConfig()
    vad: VADConfig = VADConfig()
    rag: RAGConfig = RAGConfig()
    audio: AudioConfig = AudioConfig()
    uploads: UploadsConfig = UploadsConfig()
    logging: LoggingConfig = LoggingConfig()
    redis: RedisConfig = RedisConfig()
    security: SecurityConfig


class Settings(BaseSettings):
    """Environment settings."""
    secret_key: str = "change-this-in-production"
    openai_api_key: Optional[str] = None
    anthropic_api_key: Optional[str] = None
    google_api_key: Optional[str] = None
    azure_speech_key: Optional[str] = None
    azure_speech_region: Optional[str] = None
    database_url: Optional[str] = None
    redis_url: Optional[str] = None
    debug: bool = True
    log_level: str = "INFO"

    class Config:
        env_file = ".env"


def load_config(config_path: str = "config.yaml") -> Config:
    """Load configuration from YAML file."""
    config_file = Path(config_path)
    if not config_file.exists():
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
    
    with open(config_file, "r", encoding="utf-8") as f:
        config_data = yaml.safe_load(f)
    
    # Load environment settings
    settings = Settings()
    
    # Replace environment variables in config
    config_str = yaml.dump(config_data)
    for key, value in settings.dict().items():
        if value is not None:
            config_str = config_str.replace(f"${{{key.upper()}}}", str(value))
    
    config_data = yaml.safe_load(config_str)
    
    return Config(**config_data)


# Global configuration instance
config: Optional[Config] = None


def get_config() -> Config:
    """Get the global configuration instance."""
    global config
    if config is None:
        config = load_config()
    return config
