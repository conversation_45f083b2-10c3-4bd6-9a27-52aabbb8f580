"""Models API endpoints."""

from typing import List

from fastapi import APIRouter
from pydantic import BaseModel

from ..config import get_config
from ..services.llm import LLMService

router = APIRouter()


class ModelInfo(BaseModel):
    """Model information."""
    name: str
    display_name: str
    provider: str
    max_tokens: int
    temperature: float
    available: bool = True


class ProviderInfo(BaseModel):
    """Provider information."""
    name: str
    display_name: str
    models: List[ModelInfo]
    available: bool = True


@router.get("/providers", response_model=List[ProviderInfo])
async def get_providers():
    """Get all available LLM providers and their models."""
    config = get_config()
    providers = []
    
    for provider_name, provider_config in config.llm.providers.items():
        models = []
        for model_config in provider_config.models:
            models.append(ModelInfo(
                name=model_config.name,
                display_name=model_config.display_name,
                provider=provider_name,
                max_tokens=model_config.max_tokens,
                temperature=model_config.temperature,
                available=True  # TODO: Check actual availability
            ))
        
        provider_display_names = {
            "ollama": "Ollama (Local)",
            "openai": "OpenAI",
            "claude": "Anthropic Claude",
            "gemini": "Google Gemini"
        }
        
        providers.append(ProviderInfo(
            name=provider_name,
            display_name=provider_display_names.get(provider_name, provider_name.title()),
            models=models,
            available=True  # TODO: Check actual availability
        ))
    
    return providers


@router.get("/models", response_model=List[ModelInfo])
async def get_models():
    """Get all available models across all providers."""
    config = get_config()
    models = []
    
    for provider_name, provider_config in config.llm.providers.items():
        for model_config in provider_config.models:
            models.append(ModelInfo(
                name=model_config.name,
                display_name=model_config.display_name,
                provider=provider_name,
                max_tokens=model_config.max_tokens,
                temperature=model_config.temperature,
                available=True  # TODO: Check actual availability
            ))
    
    return models


@router.get("/models/{provider_name}", response_model=List[ModelInfo])
async def get_models_by_provider(provider_name: str):
    """Get models for a specific provider."""
    config = get_config()
    
    if provider_name not in config.llm.providers:
        return []
    
    provider_config = config.llm.providers[provider_name]
    models = []
    
    for model_config in provider_config.models:
        models.append(ModelInfo(
            name=model_config.name,
            display_name=model_config.display_name,
            provider=provider_name,
            max_tokens=model_config.max_tokens,
            temperature=model_config.temperature,
            available=True  # TODO: Check actual availability
        ))
    
    return models


@router.get("/models/{provider_name}/{model_name}/status")
async def get_model_status(provider_name: str, model_name: str):
    """Check if a specific model is available."""
    # TODO: Implement actual model availability checking
    return {
        "provider": provider_name,
        "model": model_name,
        "available": True,
        "status": "ready",
        "last_checked": "2024-01-01T00:00:00Z"
    }
