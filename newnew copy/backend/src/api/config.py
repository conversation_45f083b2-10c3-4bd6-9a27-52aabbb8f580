"""Configuration API endpoints."""

from typing import Dict, Any

from fastapi import APIRouter
from pydantic import BaseModel

from ..config import get_config

router = APIRouter()


class ConfigResponse(BaseModel):
    """Configuration response."""
    server: Dict[str, Any]
    llm: Dict[str, Any]
    asr: Dict[str, Any]
    tts: Dict[str, Any]
    vad: Dict[str, Any]
    rag: Dict[str, Any]
    audio: Dict[str, Any]
    uploads: Dict[str, Any]


@router.get("/", response_model=ConfigResponse)
async def get_config_info():
    """Get current configuration (without sensitive data)."""
    config = get_config()
    
    # Remove sensitive information
    safe_config = {
        "server": {
            "host": config.server.host,
            "port": config.server.port,
            "debug": config.server.debug,
        },
        "llm": {
            "default_provider": config.llm.default_provider,
            "providers": {
                name: {
                    "models": [
                        {
                            "name": model.name,
                            "display_name": model.display_name,
                            "max_tokens": model.max_tokens,
                            "temperature": model.temperature,
                        }
                        for model in provider.models
                    ]
                }
                for name, provider in config.llm.providers.items()
            },
        },
        "asr": {
            "default_engine": config.asr.default_engine,
            "engines": list(config.asr.engines.keys()),
        },
        "tts": {
            "default_engine": config.tts.default_engine,
            "engines": list(config.tts.engines.keys()),
        },
        "vad": {
            "engine": config.vad.engine,
            "threshold": config.vad.threshold,
        },
        "rag": {
            "enabled": config.rag.enabled,
            "vector_store": config.rag.vector_store,
            "embedding_model": config.rag.embedding_model,
            "chunk_size": config.rag.chunk_size,
            "max_results": config.rag.max_results,
        },
        "audio": {
            "sample_rate": config.audio.sample_rate,
            "channels": config.audio.channels,
            "format": config.audio.format,
        },
        "uploads": {
            "max_file_size": config.uploads.max_file_size,
            "allowed_extensions": config.uploads.allowed_extensions,
        },
    }
    
    return ConfigResponse(**safe_config)


@router.get("/audio")
async def get_audio_config():
    """Get audio configuration."""
    config = get_config()
    return {
        "sample_rate": config.audio.sample_rate,
        "channels": config.audio.channels,
        "chunk_size": config.audio.chunk_size,
        "format": config.audio.format,
    }


@router.get("/uploads")
async def get_uploads_config():
    """Get file upload configuration."""
    config = get_config()
    return {
        "max_file_size": config.uploads.max_file_size,
        "allowed_extensions": config.uploads.allowed_extensions,
    }


@router.get("/rag")
async def get_rag_config():
    """Get RAG configuration."""
    config = get_config()
    return {
        "enabled": config.rag.enabled,
        "vector_store": config.rag.vector_store,
        "embedding_model": config.rag.embedding_model,
        "chunk_size": config.rag.chunk_size,
        "chunk_overlap": config.rag.chunk_overlap,
        "max_results": config.rag.max_results,
    }
