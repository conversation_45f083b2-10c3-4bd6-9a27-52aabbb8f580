"""API routes package."""

from fastapi import APIRouter

from .chat import router as chat_router
from .config import router as config_router
from .files import router as files_router
from .models import router as models_router

router = APIRouter()

# Include all sub-routers
router.include_router(chat_router, prefix="/chat", tags=["chat"])
router.include_router(config_router, prefix="/config", tags=["config"])
router.include_router(files_router, prefix="/files", tags=["files"])
router.include_router(models_router, prefix="/models", tags=["models"])
