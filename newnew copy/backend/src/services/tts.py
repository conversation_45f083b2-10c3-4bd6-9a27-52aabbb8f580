"""TTS (Text-to-Speech) service."""

import asyncio
import base64
import io
import tempfile
from typing import Optional

import httpx
from loguru import logger

from ..config import get_config


class TTSService:
    """Service for text-to-speech conversion."""
    
    def __init__(self):
        self.config = get_config()
    
    async def synthesize(self, text: str, voice: str = None) -> Optional[str]:
        """Synthesize text to speech and return base64 encoded audio."""
        engine = self.config.tts.default_engine
        
        try:
            if engine == "edge_tts":
                return await self._synthesize_edge_tts(text, voice)
            elif engine == "openai_tts":
                return await self._synthesize_openai_tts(text, voice)
            elif engine == "azure_tts":
                return await self._synthesize_azure_tts(text, voice)
            else:
                raise ValueError(f"Unsupported TTS engine: {engine}")
        
        except Exception as e:
            logger.error(f"TTS synthesis failed: {str(e)}")
            return None
    
    async def _synthesize_edge_tts(self, text: str, voice: str = None) -> Optional[str]:
        """Synthesize using Edge TTS."""
        try:
            import edge_tts
            
            engine_config = self.config.tts.engines.get("edge_tts", {})
            voice_name = voice or engine_config.get("voice", "en-US-AriaNeural")
            rate = engine_config.get("rate", "+0%")
            pitch = engine_config.get("pitch", "+0Hz")
            
            # Create TTS communicator
            communicate = edge_tts.Communicate(text, voice_name, rate=rate, pitch=pitch)
            
            # Generate audio
            audio_data = b""
            async for chunk in communicate.stream():
                if chunk["type"] == "audio":
                    audio_data += chunk["data"]
            
            if audio_data:
                return base64.b64encode(audio_data).decode('utf-8')
            
            return None
        
        except ImportError:
            logger.error("Edge TTS not installed. Install with: pip install edge-tts")
            return None
        except Exception as e:
            logger.error(f"Edge TTS synthesis failed: {str(e)}")
            return None
    
    async def _synthesize_openai_tts(self, text: str, voice: str = None) -> Optional[str]:
        """Synthesize using OpenAI TTS API."""
        try:
            engine_config = self.config.tts.engines.get("openai_tts", {})
            api_key = engine_config.get("api_key")
            
            if not api_key:
                logger.error("OpenAI API key not configured for TTS")
                return None
            
            voice_name = voice or engine_config.get("voice", "alloy")
            model = engine_config.get("model", "tts-1")
            
            headers = {"Authorization": f"Bearer {api_key}"}
            data = {
                "model": model,
                "input": text,
                "voice": voice_name,
                "response_format": "mp3"
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    "https://api.openai.com/v1/audio/speech",
                    headers=headers,
                    json=data
                )
                
                if response.status_code == 200:
                    audio_data = response.content
                    return base64.b64encode(audio_data).decode('utf-8')
                else:
                    logger.error(f"OpenAI TTS API error: {response.status_code} - {response.text}")
                    return None
        
        except Exception as e:
            logger.error(f"OpenAI TTS synthesis failed: {str(e)}")
            return None
    
    async def _synthesize_azure_tts(self, text: str, voice: str = None) -> Optional[str]:
        """Synthesize using Azure TTS."""
        try:
            engine_config = self.config.tts.engines.get("azure_tts", {})
            api_key = engine_config.get("api_key")
            region = engine_config.get("region")
            
            if not api_key or not region:
                logger.error("Azure TTS API key or region not configured")
                return None
            
            voice_name = voice or engine_config.get("voice", "en-US-AriaNeural")
            
            # Azure TTS endpoint
            endpoint = f"https://{region}.tts.speech.microsoft.com/cognitiveservices/v1"
            
            headers = {
                "Ocp-Apim-Subscription-Key": api_key,
                "Content-Type": "application/ssml+xml",
                "X-Microsoft-OutputFormat": "audio-16khz-128kbitrate-mono-mp3"
            }
            
            # SSML format
            ssml = f"""
            <speak version='1.0' xml:lang='en-US'>
                <voice xml:lang='en-US' xml:gender='Female' name='{voice_name}'>
                    {text}
                </voice>
            </speak>
            """
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    endpoint,
                    headers=headers,
                    data=ssml.encode('utf-8')
                )
                
                if response.status_code == 200:
                    audio_data = response.content
                    return base64.b64encode(audio_data).decode('utf-8')
                else:
                    logger.error(f"Azure TTS API error: {response.status_code} - {response.text}")
                    return None
        
        except Exception as e:
            logger.error(f"Azure TTS synthesis failed: {str(e)}")
            return None
    
    async def get_available_voices(self, engine: str = None) -> list:
        """Get available voices for the TTS engine."""
        engine = engine or self.config.tts.default_engine
        
        if engine == "edge_tts":
            return await self._get_edge_tts_voices()
        elif engine == "openai_tts":
            return ["alloy", "echo", "fable", "onyx", "nova", "shimmer"]
        elif engine == "azure_tts":
            return await self._get_azure_tts_voices()
        else:
            return []
    
    async def _get_edge_tts_voices(self) -> list:
        """Get Edge TTS voices."""
        try:
            import edge_tts
            
            voices = await edge_tts.list_voices()
            return [
                {
                    "name": voice["Name"],
                    "display_name": voice["FriendlyName"],
                    "gender": voice["Gender"],
                    "locale": voice["Locale"]
                }
                for voice in voices
            ]
        
        except ImportError:
            logger.error("Edge TTS not installed")
            return []
        except Exception as e:
            logger.error(f"Failed to get Edge TTS voices: {str(e)}")
            return []
    
    async def _get_azure_tts_voices(self) -> list:
        """Get Azure TTS voices."""
        try:
            engine_config = self.config.tts.engines.get("azure_tts", {})
            api_key = engine_config.get("api_key")
            region = engine_config.get("region")
            
            if not api_key or not region:
                return []
            
            endpoint = f"https://{region}.tts.speech.microsoft.com/cognitiveservices/voices/list"
            headers = {"Ocp-Apim-Subscription-Key": api_key}
            
            async with httpx.AsyncClient() as client:
                response = await client.get(endpoint, headers=headers)
                
                if response.status_code == 200:
                    voices = response.json()
                    return [
                        {
                            "name": voice["Name"],
                            "display_name": voice["DisplayName"],
                            "gender": voice["Gender"],
                            "locale": voice["Locale"]
                        }
                        for voice in voices
                    ]
                else:
                    logger.error(f"Azure TTS voices API error: {response.status_code}")
                    return []
        
        except Exception as e:
            logger.error(f"Failed to get Azure TTS voices: {str(e)}")
            return []
