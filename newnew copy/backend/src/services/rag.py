"""RAG (Retrieval-Augmented Generation) service."""

import os
from pathlib import Path
from typing import List, Dict, Any, Optional

import chromadb
from langchain.document_loaders import PyPDFLoader, TextLoader, Docx2txtLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.embeddings import HuggingFaceEmbeddings
from loguru import logger

from ..config import get_config
from ..database import SessionLocal, Document


class RAGService:
    """Service for Retrieval-Augmented Generation."""
    
    def __init__(self):
        self.config = get_config()
        self._client = None
        self._collection = None
        self._embeddings = None
        self._text_splitter = None
    
    def is_enabled(self) -> bool:
        """Check if RAG is enabled."""
        return self.config.rag.enabled
    
    def get_client(self):
        """Get ChromaDB client."""
        if self._client is None:
            persist_dir = self.config.rag.chromadb.persist_directory
            os.makedirs(persist_dir, exist_ok=True)
            
            self._client = chromadb.PersistentClient(path=persist_dir)
        
        return self._client
    
    def get_collection(self):
        """Get or create ChromaDB collection."""
        if self._collection is None:
            client = self.get_client()
            collection_name = self.config.rag.chromadb.collection_name
            
            try:
                self._collection = client.get_collection(collection_name)
            except ValueError:
                # Collection doesn't exist, create it
                self._collection = client.create_collection(collection_name)
        
        return self._collection
    
    def get_embeddings(self):
        """Get embeddings model."""
        if self._embeddings is None:
            model_name = self.config.rag.embedding_model
            self._embeddings = HuggingFaceEmbeddings(model_name=model_name)
        
        return self._embeddings
    
    def get_text_splitter(self):
        """Get text splitter."""
        if self._text_splitter is None:
            self._text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=self.config.rag.chunk_size,
                chunk_overlap=self.config.rag.chunk_overlap,
                length_function=len,
            )
        
        return self._text_splitter
    
    async def process_document(self, document_id: int, file_path: str):
        """Process a document and add it to the vector store."""
        if not self.is_enabled():
            return
        
        try:
            # Load document
            documents = await self._load_document(file_path)
            
            if not documents:
                logger.warning(f"No content extracted from document: {file_path}")
                return
            
            # Split into chunks
            text_splitter = self.get_text_splitter()
            chunks = text_splitter.split_documents(documents)
            
            # Generate embeddings
            embeddings_model = self.get_embeddings()
            texts = [chunk.page_content for chunk in chunks]
            embeddings = embeddings_model.embed_documents(texts)
            
            # Add to ChromaDB
            collection = self.get_collection()
            
            ids = [f"doc_{document_id}_chunk_{i}" for i in range(len(chunks))]
            metadatas = [
                {
                    "document_id": document_id,
                    "chunk_index": i,
                    "source": file_path,
                    **chunk.metadata
                }
                for i, chunk in enumerate(chunks)
            ]
            
            collection.add(
                ids=ids,
                embeddings=embeddings,
                documents=texts,
                metadatas=metadatas
            )
            
            logger.info(f"Processed document {document_id}: {len(chunks)} chunks added to vector store")
        
        except Exception as e:
            logger.error(f"Failed to process document {document_id}: {str(e)}")
            raise
    
    async def _load_document(self, file_path: str) -> List[Any]:
        """Load document based on file type."""
        file_extension = Path(file_path).suffix.lower()
        
        try:
            if file_extension == ".pdf":
                loader = PyPDFLoader(file_path)
            elif file_extension == ".txt":
                loader = TextLoader(file_path, encoding="utf-8")
            elif file_extension in [".docx", ".doc"]:
                loader = Docx2txtLoader(file_path)
            else:
                logger.warning(f"Unsupported file type: {file_extension}")
                return []
            
            return loader.load()
        
        except Exception as e:
            logger.error(f"Failed to load document {file_path}: {str(e)}")
            return []
    
    async def search(self, query: str, limit: int = None) -> List[Dict[str, Any]]:
        """Search for relevant documents."""
        if not self.is_enabled():
            return []
        
        limit = limit or self.config.rag.max_results
        
        try:
            # Generate query embedding
            embeddings_model = self.get_embeddings()
            query_embedding = embeddings_model.embed_query(query)
            
            # Search in ChromaDB
            collection = self.get_collection()
            results = collection.query(
                query_embeddings=[query_embedding],
                n_results=limit,
                include=["documents", "metadatas", "distances"]
            )
            
            # Format results
            search_results = []
            if results["documents"] and results["documents"][0]:
                for i, (doc, metadata, distance) in enumerate(zip(
                    results["documents"][0],
                    results["metadatas"][0],
                    results["distances"][0]
                )):
                    search_results.append({
                        "content": doc,
                        "metadata": metadata,
                        "score": 1 - distance,  # Convert distance to similarity score
                        "rank": i + 1
                    })
            
            return search_results
        
        except Exception as e:
            logger.error(f"RAG search failed: {str(e)}")
            return []
    
    async def get_context(self, query: str, limit: int = None) -> str:
        """Get context for a query."""
        if not self.is_enabled():
            return ""
        
        results = await self.search(query, limit)
        
        if not results:
            return ""
        
        # Combine relevant documents into context
        context_parts = []
        for result in results:
            content = result["content"]
            source = result["metadata"].get("source", "Unknown")
            context_parts.append(f"Source: {source}\nContent: {content}")
        
        return "\n\n---\n\n".join(context_parts)
    
    async def remove_document(self, document_id: int):
        """Remove a document from the vector store."""
        if not self.is_enabled():
            return
        
        try:
            collection = self.get_collection()
            
            # Find all chunks for this document
            results = collection.get(
                where={"document_id": document_id},
                include=["metadatas"]
            )
            
            if results["ids"]:
                # Delete all chunks
                collection.delete(ids=results["ids"])
                logger.info(f"Removed document {document_id} from vector store")
        
        except Exception as e:
            logger.error(f"Failed to remove document {document_id} from vector store: {str(e)}")
    
    async def get_document_stats(self) -> Dict[str, Any]:
        """Get statistics about the vector store."""
        if not self.is_enabled():
            return {"enabled": False}
        
        try:
            collection = self.get_collection()
            count = collection.count()
            
            return {
                "enabled": True,
                "total_chunks": count,
                "collection_name": self.config.rag.chromadb.collection_name,
                "embedding_model": self.config.rag.embedding_model
            }
        
        except Exception as e:
            logger.error(f"Failed to get RAG stats: {str(e)}")
            return {"enabled": True, "error": str(e)}
    
    async def clear_all(self):
        """Clear all documents from the vector store."""
        if not self.is_enabled():
            return
        
        try:
            client = self.get_client()
            collection_name = self.config.rag.chromadb.collection_name
            
            # Delete and recreate collection
            try:
                client.delete_collection(collection_name)
            except ValueError:
                pass  # Collection doesn't exist
            
            self._collection = client.create_collection(collection_name)
            logger.info("Cleared all documents from vector store")
        
        except Exception as e:
            logger.error(f"Failed to clear vector store: {str(e)}")
            raise
