"""ASR (Automatic Speech Recognition) service."""

import base64
import io
import tempfile
from typing import Optional

import httpx
import numpy as np
import soundfile as sf
from loguru import logger

from ..config import get_config


class ASRService:
    """Service for speech-to-text conversion."""
    
    def __init__(self):
        self.config = get_config()
        self._whisper_model = None
    
    async def transcribe(self, audio_data: str, audio_format: str = "wav") -> str:
        """Transcribe audio data to text."""
        engine = self.config.asr.default_engine
        
        try:
            if engine == "whisper_local":
                return await self._transcribe_whisper_local(audio_data, audio_format)
            elif engine == "whisper_openai":
                return await self._transcribe_whisper_openai(audio_data, audio_format)
            elif engine == "azure_speech":
                return await self._transcribe_azure(audio_data, audio_format)
            else:
                raise ValueError(f"Unsupported ASR engine: {engine}")
        
        except Exception as e:
            logger.error(f"ASR transcription failed: {str(e)}")
            return ""
    
    async def _transcribe_whisper_local(self, audio_data: str, audio_format: str) -> str:
        """Transcribe using local Whisper model."""
        try:
            import whisper
            
            # Load model if not already loaded
            if self._whisper_model is None:
                engine_config = self.config.asr.engines.get("whisper_local", {})
                model_name = engine_config.get("model", "base")
                self._whisper_model = whisper.load_model(model_name)
            
            # Decode base64 audio data
            audio_bytes = base64.b64decode(audio_data)
            
            # Convert to numpy array
            with io.BytesIO(audio_bytes) as audio_buffer:
                audio_array, sample_rate = sf.read(audio_buffer)
            
            # Ensure mono and correct sample rate
            if len(audio_array.shape) > 1:
                audio_array = np.mean(audio_array, axis=1)
            
            if sample_rate != 16000:
                # Resample to 16kHz (Whisper's expected rate)
                import librosa
                audio_array = librosa.resample(audio_array, orig_sr=sample_rate, target_sr=16000)
            
            # Transcribe
            result = self._whisper_model.transcribe(audio_array)
            return result["text"].strip()
        
        except ImportError:
            logger.error("Whisper not installed. Install with: pip install openai-whisper")
            return ""
        except Exception as e:
            logger.error(f"Local Whisper transcription failed: {str(e)}")
            return ""
    
    async def _transcribe_whisper_openai(self, audio_data: str, audio_format: str) -> str:
        """Transcribe using OpenAI Whisper API."""
        try:
            engine_config = self.config.asr.engines.get("whisper_openai", {})
            api_key = engine_config.get("api_key")
            
            if not api_key:
                logger.error("OpenAI API key not configured for Whisper")
                return ""
            
            # Decode base64 audio data
            audio_bytes = base64.b64decode(audio_data)
            
            # Create temporary file
            with tempfile.NamedTemporaryFile(suffix=f".{audio_format}", delete=False) as temp_file:
                temp_file.write(audio_bytes)
                temp_file_path = temp_file.name
            
            # Make API request
            async with httpx.AsyncClient() as client:
                with open(temp_file_path, "rb") as audio_file:
                    files = {"file": (f"audio.{audio_format}", audio_file, f"audio/{audio_format}")}
                    data = {"model": "whisper-1"}
                    headers = {"Authorization": f"Bearer {api_key}"}
                    
                    response = await client.post(
                        "https://api.openai.com/v1/audio/transcriptions",
                        files=files,
                        data=data,
                        headers=headers
                    )
                    
                    if response.status_code == 200:
                        result = response.json()
                        return result.get("text", "").strip()
                    else:
                        logger.error(f"OpenAI Whisper API error: {response.status_code} - {response.text}")
                        return ""
        
        except Exception as e:
            logger.error(f"OpenAI Whisper transcription failed: {str(e)}")
            return ""
    
    async def _transcribe_azure(self, audio_data: str, audio_format: str) -> str:
        """Transcribe using Azure Speech Services."""
        try:
            engine_config = self.config.asr.engines.get("azure_speech", {})
            api_key = engine_config.get("api_key")
            region = engine_config.get("region")
            language = engine_config.get("language", "en-US")
            
            if not api_key or not region:
                logger.error("Azure Speech API key or region not configured")
                return ""
            
            # Decode base64 audio data
            audio_bytes = base64.b64decode(audio_data)
            
            # Azure Speech API endpoint
            endpoint = f"https://{region}.stt.speech.microsoft.com/speech/recognition/conversation/cognitiveservices/v1"
            
            headers = {
                "Ocp-Apim-Subscription-Key": api_key,
                "Content-Type": f"audio/{audio_format}; codecs=audio/pcm; samplerate=16000",
                "Accept": "application/json"
            }
            
            params = {
                "language": language,
                "format": "detailed"
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    endpoint,
                    headers=headers,
                    params=params,
                    content=audio_bytes
                )
                
                if response.status_code == 200:
                    result = response.json()
                    if "NBest" in result and result["NBest"]:
                        return result["NBest"][0].get("Display", "").strip()
                    return ""
                else:
                    logger.error(f"Azure Speech API error: {response.status_code} - {response.text}")
                    return ""
        
        except Exception as e:
            logger.error(f"Azure Speech transcription failed: {str(e)}")
            return ""
    
    def get_supported_formats(self) -> list:
        """Get supported audio formats."""
        return ["wav", "mp3", "m4a", "ogg", "flac"]
    
    def get_sample_rate(self) -> int:
        """Get expected sample rate."""
        return self.config.audio.sample_rate
