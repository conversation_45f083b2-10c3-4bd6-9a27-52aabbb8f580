"""WebSocket chat handler."""

import json
import asyncio
from typing import Dict, Any, Optional

from fastapi import <PERSON><PERSON><PERSON><PERSON>, WebSocket, WebSocketDisconnect
from loguru import logger

from ..services.chat import ChatService
from ..services.llm import LLMService
from ..services.asr import ASRService
from ..services.tts import TTSService
from ..services.rag import RAGService

router = APIRouter()


class ConnectionManager:
    """WebSocket connection manager."""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.chat_services: Dict[str, ChatService] = {}
    
    async def connect(self, websocket: WebSocket, client_id: str):
        """Accept a WebSocket connection."""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        self.chat_services[client_id] = ChatService()
        logger.info(f"Client {client_id} connected")
    
    def disconnect(self, client_id: str):
        """Remove a WebSocket connection."""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
        if client_id in self.chat_services:
            del self.chat_services[client_id]
        logger.info(f"Client {client_id} disconnected")
    
    async def send_personal_message(self, message: str, client_id: str):
        """Send a message to a specific client."""
        if client_id in self.active_connections:
            websocket = self.active_connections[client_id]
            await websocket.send_text(message)
    
    async def send_json(self, data: Dict[str, Any], client_id: str):
        """Send JSON data to a specific client."""
        if client_id in self.active_connections:
            websocket = self.active_connections[client_id]
            await websocket.send_text(json.dumps(data))


manager = ConnectionManager()


@router.websocket("/chat/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """WebSocket endpoint for chat."""
    await manager.connect(websocket, client_id)
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            message = json.loads(data)
            
            # Handle different message types
            await handle_message(message, client_id)
            
    except WebSocketDisconnect:
        manager.disconnect(client_id)
    except Exception as e:
        logger.error(f"WebSocket error for client {client_id}: {str(e)}")
        manager.disconnect(client_id)


async def handle_message(message: Dict[str, Any], client_id: str):
    """Handle incoming WebSocket messages."""
    message_type = message.get("type")
    
    try:
        if message_type == "chat":
            await handle_chat_message(message, client_id)
        elif message_type == "audio":
            await handle_audio_message(message, client_id)
        elif message_type == "config":
            await handle_config_message(message, client_id)
        elif message_type == "ping":
            await manager.send_json({"type": "pong"}, client_id)
        else:
            await manager.send_json({
                "type": "error",
                "message": f"Unknown message type: {message_type}"
            }, client_id)
    
    except Exception as e:
        logger.error(f"Error handling message: {str(e)}")
        await manager.send_json({
            "type": "error",
            "message": f"Error processing message: {str(e)}"
        }, client_id)


async def handle_chat_message(message: Dict[str, Any], client_id: str):
    """Handle chat messages."""
    content = message.get("content", "")
    conversation_id = message.get("conversation_id")
    model_name = message.get("model", "")
    provider = message.get("provider", "")
    images = message.get("images", [])
    
    if not content.strip():
        return
    
    # Get chat service for this client
    chat_service = manager.chat_services.get(client_id)
    if not chat_service:
        await manager.send_json({
            "type": "error",
            "message": "Chat service not initialized"
        }, client_id)
        return
    
    # Send typing indicator
    await manager.send_json({
        "type": "typing",
        "status": True
    }, client_id)
    
    try:
        # Get LLM service
        llm_service = LLMService()
        
        # Process with RAG if enabled
        rag_service = RAGService()
        context = await rag_service.get_context(content) if rag_service.is_enabled() else ""
        
        # Generate response
        async for chunk in llm_service.chat_stream(
            messages=[{"role": "user", "content": content}],
            model=model_name,
            provider=provider,
            context=context,
            images=images
        ):
            await manager.send_json({
                "type": "chat_chunk",
                "content": chunk,
                "conversation_id": conversation_id
            }, client_id)
        
        # Send completion signal
        await manager.send_json({
            "type": "chat_complete",
            "conversation_id": conversation_id
        }, client_id)
        
    except Exception as e:
        logger.error(f"Error in chat processing: {str(e)}")
        await manager.send_json({
            "type": "error",
            "message": f"Chat processing failed: {str(e)}"
        }, client_id)
    
    finally:
        # Stop typing indicator
        await manager.send_json({
            "type": "typing",
            "status": False
        }, client_id)


async def handle_audio_message(message: Dict[str, Any], client_id: str):
    """Handle audio messages."""
    audio_data = message.get("audio_data")
    audio_format = message.get("format", "wav")
    
    if not audio_data:
        return
    
    try:
        # Send processing indicator
        await manager.send_json({
            "type": "audio_processing",
            "status": True
        }, client_id)
        
        # Transcribe audio
        asr_service = ASRService()
        transcription = await asr_service.transcribe(audio_data, audio_format)
        
        # Send transcription
        await manager.send_json({
            "type": "transcription",
            "text": transcription
        }, client_id)
        
        # Process as chat message
        if transcription.strip():
            await handle_chat_message({
                "type": "chat",
                "content": transcription,
                "conversation_id": message.get("conversation_id"),
                "model": message.get("model", ""),
                "provider": message.get("provider", "")
            }, client_id)
    
    except Exception as e:
        logger.error(f"Error in audio processing: {str(e)}")
        await manager.send_json({
            "type": "error",
            "message": f"Audio processing failed: {str(e)}"
        }, client_id)
    
    finally:
        # Stop processing indicator
        await manager.send_json({
            "type": "audio_processing",
            "status": False
        }, client_id)


async def handle_config_message(message: Dict[str, Any], client_id: str):
    """Handle configuration messages."""
    config_type = message.get("config_type")
    
    if config_type == "get_models":
        # Return available models
        llm_service = LLMService()
        models = await llm_service.get_available_models()
        
        await manager.send_json({
            "type": "config_response",
            "config_type": "models",
            "data": models
        }, client_id)
    
    elif config_type == "test_connection":
        provider = message.get("provider")
        model = message.get("model")
        
        # Test connection to model
        llm_service = LLMService()
        is_available = await llm_service.test_model(provider, model)
        
        await manager.send_json({
            "type": "config_response",
            "config_type": "connection_test",
            "data": {
                "provider": provider,
                "model": model,
                "available": is_available
            }
        }, client_id)
