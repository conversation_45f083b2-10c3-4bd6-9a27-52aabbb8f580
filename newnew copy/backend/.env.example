# Environment Variables for Chatbot Backend
# Copy this file to .env and fill in your actual values

# Security
SECRET_KEY=your-secret-key-here-change-this-in-production

# OpenAI API
OPENAI_API_KEY=your-openai-api-key-here

# Anthropic Claude API
ANTHROPIC_API_KEY=your-anthropic-api-key-here

# Google Gemini API
GOOGLE_API_KEY=your-google-api-key-here

# Azure Speech Services
AZURE_SPEECH_KEY=your-azure-speech-key-here
AZURE_SPEECH_REGION=your-azure-region-here

# Database (for production)
DATABASE_URL=postgresql://user:password@localhost/chatbot

# Redis (for Celery)
REDIS_URL=redis://localhost:6379/0

# Development Settings
DEBUG=true
LOG_LEVEL=INFO
