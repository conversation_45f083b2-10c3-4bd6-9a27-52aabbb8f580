import { useEffect, useRef, useCallback, useState } from 'react';
import { io, Socket } from 'socket.io-client';
import { useChatStore } from '../stores/chatStore';

interface WebSocketMessage {
  type: string;
  [key: string]: any;
}

export const useWebSocket = () => {
  const socketRef = useRef<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  
  const {
    addMessage,
    updateMessage,
    setTyping,
    setProcessingAudio,
    setProviders,
    currentConversationId,
    selectedModel,
    selectedProvider,
  } = useChatStore();

  const connect = useCallback(() => {
    if (socketRef.current?.connected) {
      return;
    }

    const clientId = `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Connect to WebSocket server
    const socket = io(`ws://localhost:8000/ws/chat/${clientId}`, {
      transports: ['websocket'],
      upgrade: true,
    });

    socketRef.current = socket;

    socket.on('connect', () => {
      console.log('Connected to WebSocket server');
      setIsConnected(true);
      setConnectionError(null);
      
      // Request available models
      socket.emit('message', {
        type: 'config',
        config_type: 'get_models',
      });
    });

    socket.on('disconnect', () => {
      console.log('Disconnected from WebSocket server');
      setIsConnected(false);
    });

    socket.on('connect_error', (error) => {
      console.error('WebSocket connection error:', error);
      setConnectionError(error.message);
      setIsConnected(false);
    });

    socket.on('message', (data: string) => {
      try {
        const message: WebSocketMessage = JSON.parse(data);
        handleMessage(message);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    });

    return socket;
  }, []);

  const disconnect = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
      setIsConnected(false);
    }
  }, []);

  const sendMessage = useCallback((message: WebSocketMessage) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('message', JSON.stringify(message));
    } else {
      console.error('WebSocket not connected');
    }
  }, []);

  const handleMessage = useCallback((message: WebSocketMessage) => {
    switch (message.type) {
      case 'chat_chunk':
        handleChatChunk(message);
        break;
      case 'chat_complete':
        handleChatComplete(message);
        break;
      case 'transcription':
        handleTranscription(message);
        break;
      case 'typing':
        setTyping(message.status);
        break;
      case 'audio_processing':
        setProcessingAudio(message.status);
        break;
      case 'config_response':
        handleConfigResponse(message);
        break;
      case 'error':
        handleError(message);
        break;
      case 'pong':
        // Handle ping/pong for connection health
        break;
      default:
        console.log('Unknown message type:', message.type);
    }
  }, [setTyping, setProcessingAudio]);

  const handleChatChunk = useCallback((message: WebSocketMessage) => {
    const { content, conversation_id } = message;
    
    if (!conversation_id || !currentConversationId) return;

    // Find or create assistant message
    const conversation = useChatStore.getState().conversations.find(
      conv => conv.id === conversation_id
    );
    
    if (!conversation) return;

    const lastMessage = conversation.messages[conversation.messages.length - 1];
    
    if (lastMessage && lastMessage.role === 'assistant' && lastMessage.metadata?.processing) {
      // Update existing message
      updateMessage(lastMessage.id, {
        content: lastMessage.content + content,
      });
    } else {
      // Create new assistant message
      addMessage({
        role: 'assistant',
        content,
        conversationId: conversation_id,
        metadata: {
          processing: true,
          model: selectedModel,
          provider: selectedProvider,
        },
      });
    }
  }, [currentConversationId, selectedModel, selectedProvider, addMessage, updateMessage]);

  const handleChatComplete = useCallback((message: WebSocketMessage) => {
    const { conversation_id } = message;
    
    if (!conversation_id) return;

    // Mark the last assistant message as complete
    const conversation = useChatStore.getState().conversations.find(
      conv => conv.id === conversation_id
    );
    
    if (!conversation) return;

    const lastMessage = conversation.messages[conversation.messages.length - 1];
    
    if (lastMessage && lastMessage.role === 'assistant' && lastMessage.metadata?.processing) {
      updateMessage(lastMessage.id, {
        metadata: {
          ...lastMessage.metadata,
          processing: false,
        },
      });
    }
    
    setTyping(false);
  }, [updateMessage, setTyping]);

  const handleTranscription = useCallback((message: WebSocketMessage) => {
    const { text } = message;
    
    if (text && currentConversationId) {
      // Add user message from transcription
      addMessage({
        role: 'user',
        content: text,
        conversationId: currentConversationId,
        metadata: {
          audioTranscription: true,
        },
      });
    }
  }, [currentConversationId, addMessage]);

  const handleConfigResponse = useCallback((message: WebSocketMessage) => {
    const { config_type, data } = message;
    
    if (config_type === 'models' && data) {
      // Convert models data to providers format
      const providersMap = new Map();
      
      data.forEach((model: any) => {
        if (!providersMap.has(model.provider)) {
          providersMap.set(model.provider, {
            name: model.provider,
            displayName: model.provider.charAt(0).toUpperCase() + model.provider.slice(1),
            models: [],
            available: true,
          });
        }
        
        providersMap.get(model.provider).models.push({
          name: model.name,
          displayName: model.display_name,
          provider: model.provider,
          maxTokens: model.max_tokens,
          temperature: model.temperature,
          available: model.available,
        });
      });
      
      setProviders(Array.from(providersMap.values()));
    }
  }, [setProviders]);

  const handleError = useCallback((message: WebSocketMessage) => {
    console.error('WebSocket error:', message.message);
    setConnectionError(message.message);
  }, []);

  // Send chat message
  const sendChatMessage = useCallback((content: string, images?: string[]) => {
    if (!currentConversationId) return;

    // Add user message to store
    addMessage({
      role: 'user',
      content,
      conversationId: currentConversationId,
      metadata: {
        model: selectedModel,
        provider: selectedProvider,
      },
    });

    // Send to server
    sendMessage({
      type: 'chat',
      content,
      conversation_id: currentConversationId,
      model: selectedModel,
      provider: selectedProvider,
      images: images || [],
    });
  }, [currentConversationId, selectedModel, selectedProvider, addMessage, sendMessage]);

  // Send audio data
  const sendAudioData = useCallback((audioData: string, format: string = 'wav') => {
    if (!currentConversationId) return;

    sendMessage({
      type: 'audio',
      audio_data: audioData,
      format,
      conversation_id: currentConversationId,
      model: selectedModel,
      provider: selectedProvider,
    });
  }, [currentConversationId, selectedModel, selectedProvider, sendMessage]);

  // Send ping to keep connection alive
  useEffect(() => {
    if (!isConnected) return;

    const pingInterval = setInterval(() => {
      sendMessage({ type: 'ping' });
    }, 30000); // Ping every 30 seconds

    return () => clearInterval(pingInterval);
  }, [isConnected, sendMessage]);

  return {
    connect,
    disconnect,
    sendChatMessage,
    sendAudioData,
    isConnected,
    connectionError,
  };
};
