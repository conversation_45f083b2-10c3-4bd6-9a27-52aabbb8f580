import React from 'react';
import {
  Container,
  Typography,
  Box,
  Paper,
  Grid,
  Switch,
  FormControlLabel,
  Slider,
  TextField,
  Button,
  <PERSON><PERSON>r,
  <PERSON><PERSON>,
} from '@mui/material';
import { useNavigate } from 'react-router-dom';
import { useChatStore } from '../../stores/chatStore';

const Settings: React.FC = () => {
  const navigate = useNavigate();
  const { settings, updateSettings } = useChatStore();

  const handleSettingChange = (key: keyof typeof settings, value: any) => {
    updateSettings({ [key]: value });
  };

  const handleBack = () => {
    navigate('/');
  };

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Box sx={{ mb: 4 }}>
        <Button onClick={handleBack} sx={{ mb: 2 }}>
          ← Back to Chat
        </Button>
        <Typography variant="h4" gutterBottom>
          Settings
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Configure your chatbot preferences and behavior.
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {/* Audio Settings */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Audio Settings
            </Typography>
            
            <Box sx={{ mb: 3 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.audioEnabled}
                    onChange={(e) => handleSettingChange('audioEnabled', e.target.checked)}
                  />
                }
                label="Enable audio input/output"
              />
            </Box>

            <Box sx={{ mb: 3 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.autoPlayAudio}
                    onChange={(e) => handleSettingChange('autoPlayAudio', e.target.checked)}
                    disabled={!settings.audioEnabled}
                  />
                }
                label="Auto-play AI responses"
              />
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography gutterBottom>Voice ID</Typography>
              <TextField
                fullWidth
                size="small"
                value={settings.voiceId}
                onChange={(e) => handleSettingChange('voiceId', e.target.value)}
                placeholder="en-US-AriaNeural"
                disabled={!settings.audioEnabled}
              />
            </Box>
          </Paper>
        </Grid>

        {/* Model Settings */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Model Settings
            </Typography>
            
            <Box sx={{ mb: 3 }}>
              <Typography gutterBottom>
                Temperature: {settings.temperature}
              </Typography>
              <Slider
                value={settings.temperature}
                onChange={(_, value) => handleSettingChange('temperature', value)}
                min={0}
                max={2}
                step={0.1}
                marks={[
                  { value: 0, label: '0 (Focused)' },
                  { value: 1, label: '1 (Balanced)' },
                  { value: 2, label: '2 (Creative)' },
                ]}
                valueLabelDisplay="auto"
              />
            </Box>

            <Box sx={{ mb: 2 }}>
              <Typography gutterBottom>
                Max Tokens: {settings.maxTokens.toLocaleString()}
              </Typography>
              <Slider
                value={settings.maxTokens}
                onChange={(_, value) => handleSettingChange('maxTokens', value)}
                min={256}
                max={8192}
                step={256}
                marks={[
                  { value: 256, label: '256' },
                  { value: 1024, label: '1K' },
                  { value: 2048, label: '2K' },
                  { value: 4096, label: '4K' },
                  { value: 8192, label: '8K' },
                ]}
                valueLabelDisplay="auto"
              />
            </Box>
          </Paper>
        </Grid>

        {/* Data Management */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Data Management
            </Typography>
            
            <Alert severity="info" sx={{ mb: 3 }}>
              Your conversations are stored locally in your browser. Clearing data will permanently delete all conversations.
            </Alert>

            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Button
                variant="outlined"
                color="warning"
                onClick={() => {
                  if (window.confirm('Are you sure you want to clear all conversations? This cannot be undone.')) {
                    localStorage.removeItem('chatbot-conversations');
                    window.location.reload();
                  }
                }}
              >
                Clear All Conversations
              </Button>
              
              <Button
                variant="outlined"
                onClick={() => {
                  const data = {
                    conversations: localStorage.getItem('chatbot-conversations'),
                    settings: localStorage.getItem('chatbot-settings'),
                  };
                  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
                  const url = URL.createObjectURL(blob);
                  const a = document.createElement('a');
                  a.href = url;
                  a.download = `chatbot-backup-${new Date().toISOString().split('T')[0]}.json`;
                  a.click();
                  URL.revokeObjectURL(url);
                }}
              >
                Export Data
              </Button>
            </Box>
          </Paper>
        </Grid>

        {/* About */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              About
            </Typography>
            
            <Typography variant="body2" color="text.secondary" paragraph>
              Advanced Multimodal Chatbot v0.1.0
            </Typography>
            
            <Typography variant="body2" color="text.secondary" paragraph>
              A modern chatbot application with support for text, speech, and vision capabilities.
              Built with React, TypeScript, FastAPI, and Python.
            </Typography>

            <Divider sx={{ my: 2 }} />

            <Typography variant="body2" color="text.secondary">
              Features:
            </Typography>
            <Typography variant="body2" color="text.secondary" component="ul" sx={{ mt: 1 }}>
              <li>Multiple LLM providers (Ollama, OpenAI, Claude, Gemini)</li>
              <li>Speech-to-text and text-to-speech</li>
              <li>Image upload and analysis</li>
              <li>Document processing with RAG</li>
              <li>Real-time WebSocket communication</li>
              <li>Cross-platform Electron app</li>
            </Typography>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default Settings;
