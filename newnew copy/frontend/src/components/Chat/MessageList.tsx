import React from 'react';
import {
  Box,
  Paper,
  Typography,
  Avatar,
  Chip,
  CircularProgress,
} from '@mui/material';
import {
  Person as PersonIcon,
  <PERSON>Toy as BotIcon,
  Mic as MicIcon,
} from '@mui/icons-material';
import { Message } from '../../stores/chatStore';

interface MessageListProps {
  messages: Message[];
  conversationId: string;
}

interface MessageItemProps {
  message: Message;
}

const MessageItem: React.FC<MessageItemProps> = ({ message }) => {
  const isUser = message.role === 'user';
  const isProcessing = message.metadata?.processing;

  const formatTime = (timestamp: Date) => {
    return timestamp.toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: isUser ? 'flex-end' : 'flex-start',
        mb: 2,
        animation: 'fadeIn 0.3s ease-out',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: isUser ? 'row-reverse' : 'row',
          alignItems: 'flex-start',
          gap: 1,
          maxWidth: '70%',
        }}
      >
        {/* Avatar */}
        <Avatar
          sx={{
            width: 32,
            height: 32,
            bgcolor: isUser ? 'primary.main' : 'secondary.main',
          }}
        >
          {isUser ? <PersonIcon /> : <BotIcon />}
        </Avatar>

        {/* Message content */}
        <Box sx={{ flex: 1 }}>
          <Paper
            elevation={1}
            sx={{
              p: 2,
              bgcolor: isUser ? 'primary.main' : 'background.paper',
              color: isUser ? 'primary.contrastText' : 'text.primary',
              borderRadius: 2,
              borderTopLeftRadius: isUser ? 2 : 0.5,
              borderTopRightRadius: isUser ? 0.5 : 2,
              position: 'relative',
            }}
          >
            {/* Message text */}
            <Typography
              variant="body1"
              sx={{
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-word',
                lineHeight: 1.5,
              }}
            >
              {message.content}
            </Typography>

            {/* Processing indicator */}
            {isProcessing && (
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
                <CircularProgress size={12} />
                <Typography variant="caption" sx={{ opacity: 0.7 }}>
                  Generating response...
                </Typography>
              </Box>
            )}

            {/* Metadata chips */}
            <Box sx={{ display: 'flex', gap: 0.5, mt: 1, flexWrap: 'wrap' }}>
              {message.metadata?.audioTranscription && (
                <Chip
                  icon={<MicIcon />}
                  label="Voice"
                  size="small"
                  variant="outlined"
                  sx={{
                    height: 20,
                    fontSize: '0.6rem',
                    color: isUser ? 'primary.contrastText' : 'text.secondary',
                    borderColor: isUser ? 'primary.contrastText' : 'divider',
                  }}
                />
              )}
              
              {message.metadata?.model && (
                <Chip
                  label={message.metadata.model}
                  size="small"
                  variant="outlined"
                  sx={{
                    height: 20,
                    fontSize: '0.6rem',
                    color: isUser ? 'primary.contrastText' : 'text.secondary',
                    borderColor: isUser ? 'primary.contrastText' : 'divider',
                  }}
                />
              )}
            </Box>
          </Paper>

          {/* Timestamp */}
          <Typography
            variant="caption"
            color="text.secondary"
            sx={{
              display: 'block',
              mt: 0.5,
              textAlign: isUser ? 'right' : 'left',
            }}
          >
            {formatTime(message.timestamp)}
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};

const MessageList: React.FC<MessageListProps> = ({ messages }) => {
  if (messages.length === 0) {
    return null;
  }

  return (
    <Box sx={{ p: 1 }}>
      {messages.map((message) => (
        <MessageItem key={message.id} message={message} />
      ))}
    </Box>
  );
};

export default MessageList;
