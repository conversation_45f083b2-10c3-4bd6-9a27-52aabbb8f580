.App {
  text-align: left;
}

.App-header {
  background-color: #282c34;
  padding: 20px;
  color: white;
}

.App-link {
  color: #61dafb;
}

/* Chat interface styles */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 100vh;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.chat-input-area {
  border-top: 1px solid #e0e0e0;
  padding: 16px;
  background-color: white;
}

/* Message styles */
.message {
  max-width: 70%;
  word-wrap: break-word;
}

.message.user {
  align-self: flex-end;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.message.assistant {
  align-self: flex-start;
  background-color: #f5f5f5;
  color: #333;
}

.message-content {
  padding: 12px 16px;
  border-radius: 18px;
  line-height: 1.4;
}

.message-timestamp {
  font-size: 0.75rem;
  opacity: 0.7;
  margin-top: 4px;
  text-align: right;
}

.message.user .message-timestamp {
  text-align: left;
}

/* Sidebar styles */
.sidebar {
  width: 280px;
  background-color: #f8f9fa;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  background-color: white;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.conversation-item {
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s;
  margin-bottom: 4px;
}

.conversation-item:hover {
  background-color: rgba(102, 126, 234, 0.1);
}

.conversation-item.active {
  background-color: rgba(102, 126, 234, 0.2);
  font-weight: 500;
}

/* Header styles */
.header {
  height: 64px;
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  padding: 0 24px;
  justify-content: space-between;
}

.header-title {
  font-size: 1.25rem;
  font-weight: 500;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* Input area styles */
.input-container {
  display: flex;
  gap: 8px;
  align-items: flex-end;
}

.text-input {
  flex: 1;
  min-height: 40px;
  max-height: 120px;
  resize: none;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  padding: 10px 16px;
  font-family: inherit;
  font-size: 14px;
  outline: none;
  transition: border-color 0.2s;
}

.text-input:focus {
  border-color: #667eea;
}

.send-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s;
}

.send-button:hover {
  transform: scale(1.05);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Audio controls */
.audio-controls {
  display: flex;
  gap: 8px;
  align-items: center;
}

.audio-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 1px solid #e0e0e0;
  background-color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.audio-button:hover {
  background-color: #f5f5f5;
}

.audio-button.recording {
  background-color: #ff4444;
  color: white;
  border-color: #ff4444;
  animation: pulse 1s infinite;
}

/* File upload area */
.file-upload-area {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-bottom: 8px;
}

.file-preview {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-bottom: 8px;
}

.file-preview-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #f5f5f5;
  border-radius: 8px;
  font-size: 0.875rem;
}

.file-preview-remove {
  cursor: pointer;
  color: #999;
  font-size: 1.2rem;
  line-height: 1;
}

.file-preview-remove:hover {
  color: #ff4444;
}

/* Model selector */
.model-selector {
  min-width: 200px;
}

.model-option {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.model-name {
  font-weight: 500;
}

.model-provider {
  font-size: 0.75rem;
  opacity: 0.7;
}

/* Settings page */
.settings-container {
  padding: 24px;
  max-width: 800px;
  margin: 0 auto;
}

.settings-section {
  margin-bottom: 32px;
}

.settings-section-title {
  font-size: 1.25rem;
  font-weight: 500;
  margin-bottom: 16px;
  color: #333;
}

.settings-grid {
  display: grid;
  gap: 16px;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

/* Responsive design */
@media (max-width: 768px) {
  .sidebar {
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .message {
    max-width: 85%;
  }
  
  .header {
    padding: 0 16px;
  }
  
  .chat-input-area {
    padding: 12px;
  }
}
