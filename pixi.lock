version: 6
environments:
  default:
    channels:
    - url: https://conda.anaconda.org/conda-forge/
    indexes:
    - https://pypi.org/simple
    packages:
      linux-64:
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ca-certificates-2025.1.31-hbcca054_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cuda-version-11.8-h70ddcb2_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cudatoolkit-11.8.0-h4ba93d1_13.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cudnn-********-hbc23b4c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.43-h712a8e2_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.6.4-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.2-h7f98852_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-14.2.0-h77fa898_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-14.2.0-h69a702a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-14.2.0-h77fa898_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.6.4-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.49.0-hee588c1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-14.2.0-hc0a3c3a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-14.2.0-h4852527_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcrypt-4.4.36-hd590300_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.4.0-h7b32b05_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.12.8-h9e4cc4f_1_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8228510_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_h4845f30_101.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025a-h78e105d_0.conda
      - pypi: https://files.pythonhosted.org/packages/44/4c/03fb05f56551828ec67ceb3665e5dc51638042d204983a03b0a1541475b6/aiohappyeyeballs-2.4.6-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/17/e2/9f744cee0861af673dc271a3351f59ebd5415928e20080ab85be25641471/aiohttp-3.11.12-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/ec/6a/bc7e17a3e87a2985d3e8f4da4cd0f481060eb78fb08596c42be62c90a4d9/aiosignal-1.3.2-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/78/b6/6307fbef88d9b5ee7421e68d78a9f162e0da4900bc5f5793f6d3d0e34fb8/annotated_types-0.7.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/74/86/e81814e542d1eaeec84d2312bec93a99b9ef1d78d9bfae1fc5dd74abdf15/anthropic-0.45.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/46/eb/e7f063ad1fec6b3178a3cd82d1a3c4de82cccf283fc42746168188e1cdd5/anyio-4.8.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/fc/30/d4986a882011f9df997a55e6becd864812ccfcd821d64aac8570ee39f719/attrs-25.1.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/83/f7/9241ad7154e554730ea56271e14ad1115c278b26a81eb892eac16fabb480/azure_cognitiveservices_speech-1.42.0-py3-none-manylinux1_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/38/fc/bce832fd4fd99766c04d1ee0eead6b0ec6486fb100ae5e74c1d91292b982/certifi-2025.1.31-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/b2/d5/da47df7004cb17e4955df6a43d14b3b4ae77737dff8bf7f8f333196717bf/cffi-1.17.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/38/6f/f5fbc992a329ee4e0f288c1fe0e2ad9485ed064cac731ed2fe47dcc38cbf/chardet-5.2.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/3e/a2/513f6cbe752421f16d969e32f3583762bfd583848b763913ddab8d9bfd4f/charset_normalizer-3.4.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/7e/d4/7ebdbd03970677812aac39c869717059dbb71a4cfc033ca6e5221787892c/click-8.1.8-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a7/06/3d6badcf13db419e25b07041d9c7b4a2c331d3f4e7134445ec5df57714cd/coloredlogs-15.0.1-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/12/b3/231ffd4ab1fc9d679809f356cebee130ac7daa00d6d6f3206dd4fd137e9e/distro-1.9.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f8/37/00c211f1021f9b04dde72dcbee72ce66248519c3899a47b06f8940a67c08/edge_tts-7.0.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/8f/7d/2d6ce181d7a5f51dedb8c06206cbf0ec026a99bf145edd309f9e17c3282f/fastapi-0.115.8-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/0e/e2/b066e6e02d67bf5261a6d7539648c6da3365cc9eff3eb6d82009595d84d9/flatbuffers-25.1.24-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/af/f2/64b73a9bb86f5a89fb55450e97cd5c1f84a862d4ff90d9fd1a73ab0f64a5/frozenlist-1.5.0-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/b0/6c/5a53d632b44ef7655ac8d9b34432e13160917f9307c94b1467efd34e336e/groq-0.18.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/95/04/ff642e65ad6b90db43e668d70ffb6736436c7ce41fcc549f4e9472234127/h11-0.14.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/87/f5/72347bc88306acb359581ac4d52f23c0ef445b57157adedb9aee0cd689d2/httpcore-1.0.7-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f7/d8/b644c44acc1368938317d76ac991c9bba1166311880bcc0ac297cb9d6bd7/httptools-0.6.4-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/2a/39/e50c7c3a983047577ee07d2a9e53faf5a69493943ec3f6a384bdc792deb2/httpx-0.28.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f0/0f/310fb31e39e2d734ccaa2c0fb981ee41f7bd5056ce9bc29b2248bd569169/humanfriendly-10.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/17/61/beea645c0bf398ced8b199e377b61eb999d8e46e053bb285c91c3d3eaab0/jiter-0.8.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/0e/72/a3add0e4eec4eb9e2569554f7c70f4a3c27712f40e3284d483e88094cc0e/langdetect-1.0.9.tar.gz
      - pypi: https://files.pythonhosted.org/packages/0c/29/0348de65b8cc732daa3e33e67806420b2ae89bdce2b04af740289c5c6c8c/loguru-0.7.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/43/e3/7d92a15f894aa0c9c4b49b8ee9ac9850d6e63b03c9c32c0367a13ae62209/mpmath-1.3.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/d3/c8/529101d7176fe7dfe1d99604e48d69c5dfdcadb4f06561f465c8ef12b4df/multidict-6.1.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/0f/50/de23fde84e45f5c4fda2488c759b69990fd4512387a8632860f3ac9cd225/numpy-1.26.4-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/47/42/2f71f5680834688a9c81becbe5c5bb996fd33eaed5c66ae0606c3b1d6a02/onnxruntime-1.20.1-cp312-cp312-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/9a/b6/2e2a011b2dc27a6711376808b4cd8c922c476ea0f1420b39892117fa8563/openai-1.61.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/88/ef/eb23f262cca3c0c4eb7ab1933c3b1f03d021f2c48f54763065b6f0e321be/packaging-24.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/1c/07/ebe102777a830bca91bbb93e3479cd34c2ca5d0361b83be9dbd93104865e/propcache-0.2.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/a8/45/2ebbde52ad2be18d3675b6bee50e68cd73c9e0654de77d595540b5129df8/protobuf-5.29.3-cp38-abi3-manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/13/a3/a812df4e2dd5696d1f351d58b8fe16a405b234ad2886a0dab9183fb78109/pycparser-2.22-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f4/3c/8cc1cc84deffa6e25d2d0c688ebb80635dfdbf1dbea3e30c541c8cf4d860/pydantic-2.10.6-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/8d/f0/49129b27c43396581a635d8710dae54a791b17dfc50c70164866bbf865e3/pydantic_core-2.27.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/a6/53/d78dc063216e62fc55f6b2eebb447f6a4b0a59f55c8406376f76bf959b08/pydub-0.25.1-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/48/0a/c99fb7d7e176f8b176ef19704a32e6a9c6aafdf19ef75a187f701fc15801/pysbd-0.3.4-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6a/3e/b68c118422ec867fa7ab88444e1274aa40681c606d59ac27de5a5588f082/python_dotenv-1.0.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/94/df/e1584757c736c4fba09a3fb4f22fe625cc3367b06c6ece221e4b8c1e3023/pyttsx3-2.98-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/b9/2b/614b4752f2e127db5cc206abc23a8c19678e92b23c3db30fc86ab731d3bd/PyYAML-6.0.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/f9/9b/335f9764261e915ed497fcdeb11df5dfd6f7bf257d4a6a2a686d80da4d54/requests-2.32.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/04/70/e59c192a3ad476355e7f45fb3a87326f5219cc7c472e6b040c6c6595c8f0/ruff-0.9.5-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/b0/3c/0de11ca154e24a57b579fb648151d901326d3102115bc4f9a7a86526ce54/scipy-1.15.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/48/77/a3771191d4bac619df7dc06db14a7b22dd0007548b71ee54a81f80e2d219/sherpa_onnx-1.10.43-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/b7/ce/149a00dd41f10bc29e5921b496af8b574d8413afcd5e30dfa0ed46c2cc5e/six-1.17.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e9/44/75a9c9421471a6c4805dbf2356f7c181a29c1879239abab1ea2cc8f38b40/sniffio-1.3.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/57/5e/70bdd9579b35003a489fc850b5047beeda26328053ebadc1fb60f320f7db/soundfile-0.13.1-py2.py3-none-manylinux_2_28_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/66/b7/4a1bc231e0681ebf339337b0cd05b91dc6a0d701fa852bb812e244b7a030/srt-3.5.3.tar.gz
      - pypi: https://files.pythonhosted.org/packages/d9/61/f2b52e107b1fc8944b33ef56bf6ac4ebbe16d91b94d2b87ce013bf63fb84/starlette-0.45.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/99/ff/c87e0622b1dadea79d2fb0b25ade9ed98954c9033722eb707053d310d4f3/sympy-1.13.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/40/44/4a5f08c96eb108af5cb50b41f76142f0afa346dfa99d5296fe7202a11854/tabulate-0.9.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/5c/51/51c3f2884d7bab89af25f678447ea7d297b53b5a3b5730a7cb2ef6069f07/tomli-2.2.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/d0/30/dc54f88dd4a2b5dc8a0279bdd7270e735851848b762aeb1c1184ed1f6b14/tqdm-4.67.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/26/9f/ad63fc0248c5379346306f8668cda6e2e2e9c95e01216d2b8ffd9ff037d0/typing_extensions-4.12.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c8/19/4ec628951a74043532ca2cf5d97b7b14863931476d117c471e8e2b1eb39f/urllib3-2.3.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/61/14/33a3a1352cfa71812a3a21e8c9bfb83f60b0011f5e36f2b1399d51928209/uvicorn-0.34.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/06/a7/b4e6a19925c900be9f98bec0a75e6e8f79bb53bdeb891916609ab3958967/uvloop-0.21.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/2b/b4/9396cc61b948ef18943e7c85ecfa64cf940c88977d882da57147f62b34b1/watchfiles-1.0.4-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/5a/84/44687a29792a70e111c5c477230a72c4b957d88d16141199bf9acb7537a3/websocket_client-1.8.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/81/da/72f7caabd94652e6eb7e92ed2d3da818626e70b4f2b15a854ef60bf501ec/websockets-14.2-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/1a/e1/a097d5755d3ea8479a42856f51d97eeff7a3a7160593332d98f2709b3580/yarl-1.18.3-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: .
      win-64:
      - conda: https://conda.anaconda.org/conda-forge/win-64/bzip2-1.0.8-h2466b09_7.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/ca-certificates-2025.1.31-h56e8100_0.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/cuda-version-11.8-h70ddcb2_3.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/cudatoolkit-11.8.0-h09e9e62_13.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/cudnn-********-he6de189_3.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libexpat-2.6.4-he0c23c2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libffi-3.4.2-h8ffe710_5.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/win-64/liblzma-5.6.4-h2466b09_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libsqlite-3.49.0-h67fdade_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libzlib-1.3.1-h2466b09_2.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/libzlib-wapi-1.2.13-h2466b09_6.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/openssl-3.4.0-ha4e3fda_1.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/python-3.12.8-h3f84c4b_1_cpython.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/tk-8.6.13-h5226925_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025a-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/ucrt-10.0.22621.0-h57928b3_1.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/vc-14.3-h5fd82a7_24.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/vc14_runtime-14.42.34433-h6356254_24.conda
      - conda: https://conda.anaconda.org/conda-forge/win-64/vs2015_runtime-14.42.34433-hfef2bbc_24.conda
      - pypi: https://files.pythonhosted.org/packages/44/4c/03fb05f56551828ec67ceb3665e5dc51638042d204983a03b0a1541475b6/aiohappyeyeballs-2.4.6-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/3d/63/5eca549d34d141bcd9de50d4e59b913f3641559460c739d5e215693cb54a/aiohttp-3.11.12-cp312-cp312-win_amd64.whl
      - pypi: https://files.pythonhosted.org/packages/ec/6a/bc7e17a3e87a2985d3e8f4da4cd0f481060eb78fb08596c42be62c90a4d9/aiosignal-1.3.2-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/78/b6/6307fbef88d9b5ee7421e68d78a9f162e0da4900bc5f5793f6d3d0e34fb8/annotated_types-0.7.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/74/86/e81814e542d1eaeec84d2312bec93a99b9ef1d78d9bfae1fc5dd74abdf15/anthropic-0.45.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/46/eb/e7f063ad1fec6b3178a3cd82d1a3c4de82cccf283fc42746168188e1cdd5/anyio-4.8.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/fc/30/d4986a882011f9df997a55e6becd864812ccfcd821d64aac8570ee39f719/attrs-25.1.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/52/bb/ef7a29f5717cca646be6698d80e542446a6a442be897c8f67bf93551c672/azure_cognitiveservices_speech-1.42.0-py3-none-win_amd64.whl
      - pypi: https://files.pythonhosted.org/packages/38/fc/bce832fd4fd99766c04d1ee0eead6b0ec6486fb100ae5e74c1d91292b982/certifi-2025.1.31-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/50/b9/db34c4755a7bd1cb2d1603ac3863f22bcecbd1ba29e5ee841a4bc510b294/cffi-1.17.1-cp312-cp312-win_amd64.whl
      - pypi: https://files.pythonhosted.org/packages/38/6f/f5fbc992a329ee4e0f288c1fe0e2ad9485ed064cac731ed2fe47dcc38cbf/chardet-5.2.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/21/5b/1b390b03b1d16c7e382b561c5329f83cc06623916aab983e8ab9239c7d5c/charset_normalizer-3.4.1-cp312-cp312-win_amd64.whl
      - pypi: https://files.pythonhosted.org/packages/7e/d4/7ebdbd03970677812aac39c869717059dbb71a4cfc033ca6e5221787892c/click-8.1.8-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/d1/d6/3965ed04c63042e047cb6a3e6ed1a63a35087b6a609aa3a15ed8ac56c221/colorama-0.4.6-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a7/06/3d6badcf13db419e25b07041d9c7b4a2c331d3f4e7134445ec5df57714cd/coloredlogs-15.0.1-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/4c/44/72009bb0a0d8286f6408c9cb70552350e21e9c280bfa1ef30784b30dfc0f/comtypes-1.4.10-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/12/b3/231ffd4ab1fc9d679809f356cebee130ac7daa00d6d6f3206dd4fd137e9e/distro-1.9.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f8/37/00c211f1021f9b04dde72dcbee72ce66248519c3899a47b06f8940a67c08/edge_tts-7.0.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/8f/7d/2d6ce181d7a5f51dedb8c06206cbf0ec026a99bf145edd309f9e17c3282f/fastapi-0.115.8-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/0e/e2/b066e6e02d67bf5261a6d7539648c6da3365cc9eff3eb6d82009595d84d9/flatbuffers-25.1.24-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/b1/56/4e45136ffc6bdbfa68c29ca56ef53783ef4c2fd395f7cbf99a2624aa9aaa/frozenlist-1.5.0-cp312-cp312-win_amd64.whl
      - pypi: https://files.pythonhosted.org/packages/b0/6c/5a53d632b44ef7655ac8d9b34432e13160917f9307c94b1467efd34e336e/groq-0.18.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/95/04/ff642e65ad6b90db43e668d70ffb6736436c7ce41fcc549f4e9472234127/h11-0.14.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/87/f5/72347bc88306acb359581ac4d52f23c0ef445b57157adedb9aee0cd689d2/httpcore-1.0.7-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/12/b7/5cae71a8868e555f3f67a50ee7f673ce36eac970f029c0c5e9d584352961/httptools-0.6.4-cp312-cp312-win_amd64.whl
      - pypi: https://files.pythonhosted.org/packages/2a/39/e50c7c3a983047577ee07d2a9e53faf5a69493943ec3f6a384bdc792deb2/httpx-0.28.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f0/0f/310fb31e39e2d734ccaa2c0fb981ee41f7bd5056ce9bc29b2248bd569169/humanfriendly-10.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/41/69/6d4bbe66b3b3b4507e47aa1dd5d075919ad242b4b1115b3f80eecd443687/jiter-0.8.2-cp312-cp312-win_amd64.whl
      - pypi: https://files.pythonhosted.org/packages/0e/72/a3add0e4eec4eb9e2569554f7c70f4a3c27712f40e3284d483e88094cc0e/langdetect-1.0.9.tar.gz
      - pypi: https://files.pythonhosted.org/packages/0c/29/0348de65b8cc732daa3e33e67806420b2ae89bdce2b04af740289c5c6c8c/loguru-0.7.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/43/e3/7d92a15f894aa0c9c4b49b8ee9ac9850d6e63b03c9c32c0367a13ae62209/mpmath-1.3.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a3/bf/f332a13486b1ed0496d624bcc7e8357bb8053823e8cd4b9a18edc1d97e73/multidict-6.1.0-cp312-cp312-win_amd64.whl
      - pypi: https://files.pythonhosted.org/packages/16/2e/86f24451c2d530c88daf997cb8d6ac622c1d40d19f5a031ed68a4b73a374/numpy-1.26.4-cp312-cp312-win_amd64.whl
      - pypi: https://files.pythonhosted.org/packages/dd/80/76979e0b744307d488c79e41051117634b956612cc731f1028eb17ee7294/onnxruntime-1.20.1-cp312-cp312-win_amd64.whl
      - pypi: https://files.pythonhosted.org/packages/9a/b6/2e2a011b2dc27a6711376808b4cd8c922c476ea0f1420b39892117fa8563/openai-1.61.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/88/ef/eb23f262cca3c0c4eb7ab1933c3b1f03d021f2c48f54763065b6f0e321be/packaging-24.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/3b/77/a92c3ef994e47180862b9d7d11e37624fb1c00a16d61faf55115d970628b/propcache-0.2.1-cp312-cp312-win_amd64.whl
      - pypi: https://files.pythonhosted.org/packages/61/fa/aae8e10512b83de633f2646506a6d835b151edf4b30d18d73afd01447253/protobuf-5.29.3-cp310-abi3-win_amd64.whl
      - pypi: https://files.pythonhosted.org/packages/13/a3/a812df4e2dd5696d1f351d58b8fe16a405b234ad2886a0dab9183fb78109/pycparser-2.22-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f4/3c/8cc1cc84deffa6e25d2d0c688ebb80635dfdbf1dbea3e30c541c8cf4d860/pydantic-2.10.6-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/1f/ea/cd7209a889163b8dcca139fe32b9687dd05249161a3edda62860430457a5/pydantic_core-2.27.2-cp312-cp312-win_amd64.whl
      - pypi: https://files.pythonhosted.org/packages/a6/53/d78dc063216e62fc55f6b2eebb447f6a4b0a59f55c8406376f76bf959b08/pydub-0.25.1-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/d0/1b/2f292bbd742e369a100c91faa0483172cd91a1a422a6692055ac920946c5/pypiwin32-223-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/5a/dc/491b7661614ab97483abf2056be1deee4dc2490ecbf7bff9ab5cdbac86e1/pyreadline3-3.5.4-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/48/0a/c99fb7d7e176f8b176ef19704a32e6a9c6aafdf19ef75a187f701fc15801/pysbd-0.3.4-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6a/3e/b68c118422ec867fa7ab88444e1274aa40681c606d59ac27de5a5588f082/python_dotenv-1.0.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/94/df/e1584757c736c4fba09a3fb4f22fe625cc3367b06c6ece221e4b8c1e3023/pyttsx3-2.98-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/21/27/0c8811fbc3ca188f93b5354e7c286eb91f80a53afa4e11007ef661afa746/pywin32-308-cp312-cp312-win_amd64.whl
      - pypi: https://files.pythonhosted.org/packages/0c/e8/4f648c598b17c3d06e8753d7d13d57542b30d56e6c2dedf9c331ae56312e/PyYAML-6.0.2-cp312-cp312-win_amd64.whl
      - pypi: https://files.pythonhosted.org/packages/f9/9b/335f9764261e915ed497fcdeb11df5dfd6f7bf257d4a6a2a686d80da4d54/requests-2.32.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/b7/ad/c7a900591bd152bb47fc4882a27654ea55c7973e6d5d6396298ad3fd6638/ruff-0.9.5-py3-none-win_amd64.whl
      - pypi: https://files.pythonhosted.org/packages/ff/ba/31c7a8131152822b3a2cdeba76398ffb404d81d640de98287d236da90c49/scipy-1.15.1-cp312-cp312-win_amd64.whl
      - pypi: https://files.pythonhosted.org/packages/32/7a/1e9a31a5d07d1d3ed53f9cca128133f52fb898cc49196fe0a66a0b056c2d/sherpa_onnx-1.10.43-cp312-cp312-win_amd64.whl
      - pypi: https://files.pythonhosted.org/packages/b7/ce/149a00dd41f10bc29e5921b496af8b574d8413afcd5e30dfa0ed46c2cc5e/six-1.17.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e9/44/75a9c9421471a6c4805dbf2356f7c181a29c1879239abab1ea2cc8f38b40/sniffio-1.3.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/14/e9/6b761de83277f2f02ded7e7ea6f07828ec78e4b229b80e4ca55dd205b9dc/soundfile-0.13.1-py2.py3-none-win_amd64.whl
      - pypi: https://files.pythonhosted.org/packages/66/b7/4a1bc231e0681ebf339337b0cd05b91dc6a0d701fa852bb812e244b7a030/srt-3.5.3.tar.gz
      - pypi: https://files.pythonhosted.org/packages/d9/61/f2b52e107b1fc8944b33ef56bf6ac4ebbe16d91b94d2b87ce013bf63fb84/starlette-0.45.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/99/ff/c87e0622b1dadea79d2fb0b25ade9ed98954c9033722eb707053d310d4f3/sympy-1.13.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/40/44/4a5f08c96eb108af5cb50b41f76142f0afa346dfa99d5296fe7202a11854/tabulate-0.9.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/ef/60/9b9638f081c6f1261e2688bd487625cd1e660d0a85bd469e91d8db969734/tomli-2.2.1-cp312-cp312-win_amd64.whl
      - pypi: https://files.pythonhosted.org/packages/d0/30/dc54f88dd4a2b5dc8a0279bdd7270e735851848b762aeb1c1184ed1f6b14/tqdm-4.67.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/26/9f/ad63fc0248c5379346306f8668cda6e2e2e9c95e01216d2b8ffd9ff037d0/typing_extensions-4.12.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c8/19/4ec628951a74043532ca2cf5d97b7b14863931476d117c471e8e2b1eb39f/urllib3-2.3.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/61/14/33a3a1352cfa71812a3a21e8c9bfb83f60b0011f5e36f2b1399d51928209/uvicorn-0.34.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/ea/94/b0165481bff99a64b29e46e07ac2e0df9f7a957ef13bec4ceab8515f44e3/watchfiles-1.0.4-cp312-cp312-win_amd64.whl
      - pypi: https://files.pythonhosted.org/packages/5a/84/44687a29792a70e111c5c477230a72c4b957d88d16141199bf9acb7537a3/websocket_client-1.8.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/b3/7d/32cdb77990b3bdc34a306e0a0f73a1275221e9a66d869f6ff833c95b56ef/websockets-14.2-cp312-cp312-win_amd64.whl
      - pypi: https://files.pythonhosted.org/packages/e1/07/c6fe3ad3e685340704d314d765b7912993bcb8dc198f0e7a89382d37974b/win32_setctime-1.2.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/34/45/0e055320daaabfc169b21ff6174567b2c910c45617b0d79c68d7ab349b02/yarl-1.18.3-cp312-cp312-win_amd64.whl
      - pypi: .
packages:
- conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
  sha256: fe51de6107f9edc7aa4f786a70f4a883943bc9d39b3bb7307c04c41410990726
  md5: d7c89558ba9fa0495403155b64376d81
  license: None
  purls: []
  size: 2562
  timestamp: 1578324546067
- conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
  build_number: 16
  sha256: fbe2c5e56a653bebb982eda4876a9178aedfc2b545f25d0ce9c4c0b508253d22
  md5: 73aaf86a425cc6e73fcf236a5a46396d
  depends:
  - _libgcc_mutex 0.1 conda_forge
  - libgomp >=7.5.0
  constrains:
  - openmp_impl 9999
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 23621
  timestamp: 1650670423406
- pypi: https://files.pythonhosted.org/packages/44/4c/03fb05f56551828ec67ceb3665e5dc51638042d204983a03b0a1541475b6/aiohappyeyeballs-2.4.6-py3-none-any.whl
  name: aiohappyeyeballs
  version: 2.4.6
  sha256: 147ec992cf873d74f5062644332c539fcd42956dc69453fe5204195e560517e1
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/17/e2/9f744cee0861af673dc271a3351f59ebd5415928e20080ab85be25641471/aiohttp-3.11.12-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: aiohttp
  version: 3.11.12
  sha256: 6dfe7f984f28a8ae94ff3a7953cd9678550dbd2a1f9bda5dd9c5ae627744c78e
  requires_dist:
  - aiohappyeyeballs>=2.3.0
  - aiosignal>=1.1.2
  - async-timeout>=4.0,<6.0 ; python_full_version < '3.11'
  - attrs>=17.3.0
  - frozenlist>=1.1.1
  - multidict>=4.5,<7.0
  - propcache>=0.2.0
  - yarl>=1.17.0,<2.0
  - aiodns>=3.2.0 ; (sys_platform == 'darwin' and extra == 'speedups') or (sys_platform == 'linux' and extra == 'speedups')
  - brotli ; platform_python_implementation == 'CPython' and extra == 'speedups'
  - brotlicffi ; platform_python_implementation != 'CPython' and extra == 'speedups'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/3d/63/5eca549d34d141bcd9de50d4e59b913f3641559460c739d5e215693cb54a/aiohttp-3.11.12-cp312-cp312-win_amd64.whl
  name: aiohttp
  version: 3.11.12
  sha256: 54775858c7f2f214476773ce785a19ee81d1294a6bedc5cc17225355aab74802
  requires_dist:
  - aiohappyeyeballs>=2.3.0
  - aiosignal>=1.1.2
  - async-timeout>=4.0,<6.0 ; python_full_version < '3.11'
  - attrs>=17.3.0
  - frozenlist>=1.1.1
  - multidict>=4.5,<7.0
  - propcache>=0.2.0
  - yarl>=1.17.0,<2.0
  - aiodns>=3.2.0 ; (sys_platform == 'darwin' and extra == 'speedups') or (sys_platform == 'linux' and extra == 'speedups')
  - brotli ; platform_python_implementation == 'CPython' and extra == 'speedups'
  - brotlicffi ; platform_python_implementation != 'CPython' and extra == 'speedups'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/ec/6a/bc7e17a3e87a2985d3e8f4da4cd0f481060eb78fb08596c42be62c90a4d9/aiosignal-1.3.2-py2.py3-none-any.whl
  name: aiosignal
  version: 1.3.2
  sha256: 45cde58e409a301715980c2b01d0c28bdde3770d8290b5eb2173759d9acb31a5
  requires_dist:
  - frozenlist>=1.1.0
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/78/b6/6307fbef88d9b5ee7421e68d78a9f162e0da4900bc5f5793f6d3d0e34fb8/annotated_types-0.7.0-py3-none-any.whl
  name: annotated-types
  version: 0.7.0
  sha256: 1f02e8b43a8fbbc3f3e0d4f0f4bfc8131bcb4eebe8849b8e5c773f3a1c582a53
  requires_dist:
  - typing-extensions>=4.0.0 ; python_full_version < '3.9'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/74/86/e81814e542d1eaeec84d2312bec93a99b9ef1d78d9bfae1fc5dd74abdf15/anthropic-0.45.2-py3-none-any.whl
  name: anthropic
  version: 0.45.2
  sha256: ecd746f7274451dfcb7e1180571ead624c7e1195d1d46cb7c70143d2aedb4d35
  requires_dist:
  - anyio>=3.5.0,<5
  - distro>=1.7.0,<2
  - httpx>=0.23.0,<1
  - jiter>=0.4.0,<1
  - pydantic>=1.9.0,<3
  - sniffio
  - typing-extensions>=4.10,<5
  - boto3>=1.28.57 ; extra == 'bedrock'
  - botocore>=1.31.57 ; extra == 'bedrock'
  - google-auth>=2,<3 ; extra == 'vertex'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/46/eb/e7f063ad1fec6b3178a3cd82d1a3c4de82cccf283fc42746168188e1cdd5/anyio-4.8.0-py3-none-any.whl
  name: anyio
  version: 4.8.0
  sha256: b5011f270ab5eb0abf13385f851315585cc37ef330dd88e27ec3d34d651fd47a
  requires_dist:
  - exceptiongroup>=1.0.2 ; python_full_version < '3.11'
  - idna>=2.8
  - sniffio>=1.1
  - typing-extensions>=4.5 ; python_full_version < '3.13'
  - trio>=0.26.1 ; extra == 'trio'
  - anyio[trio] ; extra == 'test'
  - coverage[toml]>=7 ; extra == 'test'
  - exceptiongroup>=1.2.0 ; extra == 'test'
  - hypothesis>=4.0 ; extra == 'test'
  - psutil>=5.9 ; extra == 'test'
  - pytest>=7.0 ; extra == 'test'
  - trustme ; extra == 'test'
  - truststore>=0.9.1 ; python_full_version >= '3.10' and extra == 'test'
  - uvloop>=0.21 ; python_full_version < '3.14' and platform_python_implementation == 'CPython' and platform_system != 'Windows' and extra == 'test'
  - packaging ; extra == 'doc'
  - sphinx~=7.4 ; extra == 'doc'
  - sphinx-rtd-theme ; extra == 'doc'
  - sphinx-autodoc-typehints>=1.2.0 ; extra == 'doc'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/fc/30/d4986a882011f9df997a55e6becd864812ccfcd821d64aac8570ee39f719/attrs-25.1.0-py3-none-any.whl
  name: attrs
  version: 25.1.0
  sha256: c75a69e28a550a7e93789579c22aa26b0f5b83b75dc4e08fe092980051e1090a
  requires_dist:
  - cloudpickle ; platform_python_implementation == 'CPython' and extra == 'benchmark'
  - hypothesis ; extra == 'benchmark'
  - mypy>=1.11.1 ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'benchmark'
  - pympler ; extra == 'benchmark'
  - pytest-codspeed ; extra == 'benchmark'
  - pytest-mypy-plugins ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'benchmark'
  - pytest-xdist[psutil] ; extra == 'benchmark'
  - pytest>=4.3.0 ; extra == 'benchmark'
  - cloudpickle ; platform_python_implementation == 'CPython' and extra == 'cov'
  - coverage[toml]>=5.3 ; extra == 'cov'
  - hypothesis ; extra == 'cov'
  - mypy>=1.11.1 ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'cov'
  - pympler ; extra == 'cov'
  - pytest-mypy-plugins ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'cov'
  - pytest-xdist[psutil] ; extra == 'cov'
  - pytest>=4.3.0 ; extra == 'cov'
  - cloudpickle ; platform_python_implementation == 'CPython' and extra == 'dev'
  - hypothesis ; extra == 'dev'
  - mypy>=1.11.1 ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'dev'
  - pre-commit-uv ; extra == 'dev'
  - pympler ; extra == 'dev'
  - pytest-mypy-plugins ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'dev'
  - pytest-xdist[psutil] ; extra == 'dev'
  - pytest>=4.3.0 ; extra == 'dev'
  - cogapp ; extra == 'docs'
  - furo ; extra == 'docs'
  - myst-parser ; extra == 'docs'
  - sphinx ; extra == 'docs'
  - sphinx-notfound-page ; extra == 'docs'
  - sphinxcontrib-towncrier ; extra == 'docs'
  - towncrier<24.7 ; extra == 'docs'
  - cloudpickle ; platform_python_implementation == 'CPython' and extra == 'tests'
  - hypothesis ; extra == 'tests'
  - mypy>=1.11.1 ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'tests'
  - pympler ; extra == 'tests'
  - pytest-mypy-plugins ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'tests'
  - pytest-xdist[psutil] ; extra == 'tests'
  - pytest>=4.3.0 ; extra == 'tests'
  - mypy>=1.11.1 ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'tests-mypy'
  - pytest-mypy-plugins ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'tests-mypy'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/52/bb/ef7a29f5717cca646be6698d80e542446a6a442be897c8f67bf93551c672/azure_cognitiveservices_speech-1.42.0-py3-none-win_amd64.whl
  name: azure-cognitiveservices-speech
  version: 1.42.0
  sha256: 32076ee03b3b402a2e8841f2c21e5cd54dc3ffbf5af183426344727298c8bbd4
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/83/f7/9241ad7154e554730ea56271e14ad1115c278b26a81eb892eac16fabb480/azure_cognitiveservices_speech-1.42.0-py3-none-manylinux1_x86_64.whl
  name: azure-cognitiveservices-speech
  version: 1.42.0
  sha256: 90890a147499239f37b0b1a5112c51820b90fa2b5adafa0df4da6cc0c211887f
  requires_python: '>=3.7'
- conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
  sha256: 5ced96500d945fb286c9c838e54fa759aa04a7129c59800f0846b4335cee770d
  md5: 62ee74e96c5ebb0af99386de58cf9553
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  license: bzip2-1.0.6
  license_family: BSD
  purls: []
  size: 252783
  timestamp: 1720974456583
- conda: https://conda.anaconda.org/conda-forge/win-64/bzip2-1.0.8-h2466b09_7.conda
  sha256: 35a5dad92e88fdd7fc405e864ec239486f4f31eec229e31686e61a140a8e573b
  md5: 276e7ffe9ffe39688abc665ef0f45596
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: bzip2-1.0.6
  license_family: BSD
  purls: []
  size: 54927
  timestamp: 1720974860185
- conda: https://conda.anaconda.org/conda-forge/linux-64/ca-certificates-2025.1.31-hbcca054_0.conda
  sha256: bf832198976d559ab44d6cdb315642655547e26d826e34da67cbee6624cda189
  md5: 19f3a56f68d2fd06c516076bff482c52
  license: ISC
  purls: []
  size: 158144
  timestamp: 1738298224464
- conda: https://conda.anaconda.org/conda-forge/win-64/ca-certificates-2025.1.31-h56e8100_0.conda
  sha256: 1bedccdf25a3bd782d6b0e57ddd97cdcda5501716009f2de4479a779221df155
  md5: 5304a31607974dfc2110dfbb662ed092
  license: ISC
  purls: []
  size: 158690
  timestamp: 1738298232550
- pypi: https://files.pythonhosted.org/packages/38/fc/bce832fd4fd99766c04d1ee0eead6b0ec6486fb100ae5e74c1d91292b982/certifi-2025.1.31-py3-none-any.whl
  name: certifi
  version: 2025.1.31
  sha256: ca78db4565a652026a4db2bcdf68f2fb589ea80d0be70e03929ed730746b84fe
  requires_python: '>=3.6'
- pypi: https://files.pythonhosted.org/packages/50/b9/db34c4755a7bd1cb2d1603ac3863f22bcecbd1ba29e5ee841a4bc510b294/cffi-1.17.1-cp312-cp312-win_amd64.whl
  name: cffi
  version: 1.17.1
  sha256: 51392eae71afec0d0c8fb1a53b204dbb3bcabcb3c9b807eedf3e1e6ccf2de903
  requires_dist:
  - pycparser
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/b2/d5/da47df7004cb17e4955df6a43d14b3b4ae77737dff8bf7f8f333196717bf/cffi-1.17.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: cffi
  version: 1.17.1
  sha256: b62ce867176a75d03a665bad002af8e6d54644fad99a3c70905c543130e39d93
  requires_dist:
  - pycparser
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/38/6f/f5fbc992a329ee4e0f288c1fe0e2ad9485ed064cac731ed2fe47dcc38cbf/chardet-5.2.0-py3-none-any.whl
  name: chardet
  version: 5.2.0
  sha256: e1cf59446890a00105fe7b7912492ea04b6e6f06d4b742b2c788469e34c82970
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/21/5b/1b390b03b1d16c7e382b561c5329f83cc06623916aab983e8ab9239c7d5c/charset_normalizer-3.4.1-cp312-cp312-win_amd64.whl
  name: charset-normalizer
  version: 3.4.1
  sha256: 6ff8a4a60c227ad87030d76e99cd1698345d4491638dfa6673027c48b3cd395f
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/3e/a2/513f6cbe752421f16d969e32f3583762bfd583848b763913ddab8d9bfd4f/charset_normalizer-3.4.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: charset-normalizer
  version: 3.4.1
  sha256: bc2722592d8998c870fa4e290c2eec2c1569b87fe58618e67d38b4665dfa680d
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/7e/d4/7ebdbd03970677812aac39c869717059dbb71a4cfc033ca6e5221787892c/click-8.1.8-py3-none-any.whl
  name: click
  version: 8.1.8
  sha256: 63c132bbbed01578a06712a2d1f497bb62d9c1c0d329b7903a866228027263b2
  requires_dist:
  - colorama ; platform_system == 'Windows'
  - importlib-metadata ; python_full_version < '3.8'
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/d1/d6/3965ed04c63042e047cb6a3e6ed1a63a35087b6a609aa3a15ed8ac56c221/colorama-0.4.6-py2.py3-none-any.whl
  name: colorama
  version: 0.4.6
  sha256: 4f1d9991f5acc0ca119f9d443620b77f9d6b33703e51011c16baf57afb285fc6
  requires_python: '>=2.7,!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*,!=3.4.*,!=3.5.*,!=3.6.*'
- pypi: https://files.pythonhosted.org/packages/a7/06/3d6badcf13db419e25b07041d9c7b4a2c331d3f4e7134445ec5df57714cd/coloredlogs-15.0.1-py2.py3-none-any.whl
  name: coloredlogs
  version: 15.0.1
  sha256: 612ee75c546f53e92e70049c9dbfcc18c935a2b9a53b66085ce9ef6a6e5c0934
  requires_dist:
  - humanfriendly>=9.1
  - capturer>=2.4 ; extra == 'cron'
  requires_python: '>=2.7,!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*,!=3.4.*'
- pypi: https://files.pythonhosted.org/packages/4c/44/72009bb0a0d8286f6408c9cb70552350e21e9c280bfa1ef30784b30dfc0f/comtypes-1.4.10-py3-none-any.whl
  name: comtypes
  version: 1.4.10
  sha256: e078555721ee7ab40648a3363697d420b845b323e5944b55846e96aff97d2534
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/noarch/cuda-version-11.8-h70ddcb2_3.conda
  sha256: 53e0ffc14ea2f2b8c12320fd2aa38b01112763eba851336ff5953b436ae61259
  md5: 670f0e1593b8c1d84f57ad5fe5256799
  constrains:
  - cudatoolkit 11.8|11.8.*
  - __cuda >=11
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  purls: []
  size: 21043
  timestamp: 1709765911943
- conda: https://conda.anaconda.org/conda-forge/linux-64/cudatoolkit-11.8.0-h4ba93d1_13.conda
  sha256: 1797bacaf5350f272413c7f50787c01aef0e8eb955df0f0db144b10be2819752
  md5: eb43f5f1f16e2fad2eba22219c3e499b
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  constrains:
  - __cuda >=11
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  purls: []
  size: 715605660
  timestamp: 1706881738892
- conda: https://conda.anaconda.org/conda-forge/win-64/cudatoolkit-11.8.0-h09e9e62_13.conda
  sha256: 45491dddc59d4ae8abba3640056da3c3a81b93e87a5b56f336f5ffabf58d14b3
  md5: 56d440fefc5a01e631bbdb9e1f1701ad
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  constrains:
  - __cuda >=11
  license: LicenseRef-NVIDIA-End-User-License-Agreement
  purls: []
  size: 726105666
  timestamp: 1706883637901
- conda: https://conda.anaconda.org/conda-forge/linux-64/cudnn-********-hbc23b4c_3.conda
  sha256: c553234d447d9938556f067aba7a4686c8e5427e03e740e67199da3782cc420c
  md5: 4a2d5fab2871d95544de4e1752948d0f
  depends:
  - __glibc >=2.17
  - __glibc >=2.17,<3.0.a0
  - cuda-version >=11.0,<12.0a0
  - cudatoolkit 11.*
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  - libzlib >=1.2.13,<2.0.0a0
  license: LicenseRef-cuDNN-Software-License-Agreement
  purls: []
  size: 465458543
  timestamp: 1710307873021
- conda: https://conda.anaconda.org/conda-forge/win-64/cudnn-********-he6de189_3.conda
  sha256: 140b25e4df96d317de8a2ebf744b9e8e763a8a146acfdd8acd411659c6dafd80
  md5: 083d66898b460391c0b912e92e141250
  depends:
  - cuda-version >=11.0,<12.0a0
  - cudatoolkit 11.*
  - libzlib-wapi >=1.2.13,<1.3.0a0
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: LicenseRef-cuDNN-Software-License-Agreement
  purls: []
  size: 458138472
  timestamp: 1710308712723
- pypi: https://files.pythonhosted.org/packages/12/b3/231ffd4ab1fc9d679809f356cebee130ac7daa00d6d6f3206dd4fd137e9e/distro-1.9.0-py3-none-any.whl
  name: distro
  version: 1.9.0
  sha256: 7bffd925d65168f85027d8da9af6bddab658135b840670a223589bc0c8ef02b2
  requires_python: '>=3.6'
- pypi: https://files.pythonhosted.org/packages/f8/37/00c211f1021f9b04dde72dcbee72ce66248519c3899a47b06f8940a67c08/edge_tts-7.0.0-py3-none-any.whl
  name: edge-tts
  version: 7.0.0
  sha256: c99e91caba83c28e6f1fff1098a8188f541ba9615944c7a6f8f5625e02848044
  requires_dist:
  - aiohttp>=3.8.0,<4.0.0
  - certifi>=2023.11.17
  - srt>=3.4.1,<4.0.0
  - tabulate>=0.4.4,<1.0.0
  - typing-extensions>=4.1.0,<5.0.0
  - black ; extra == 'dev'
  - isort ; extra == 'dev'
  - mypy ; extra == 'dev'
  - pylint ; extra == 'dev'
  - types-tabulate ; extra == 'dev'
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/8f/7d/2d6ce181d7a5f51dedb8c06206cbf0ec026a99bf145edd309f9e17c3282f/fastapi-0.115.8-py3-none-any.whl
  name: fastapi
  version: 0.115.8
  sha256: 753a96dd7e036b34eeef8babdfcfe3f28ff79648f86551eb36bfc1b0bf4a8cbf
  requires_dist:
  - starlette>=0.40.0,<0.46.0
  - pydantic>=1.7.4,!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0
  - typing-extensions>=4.8.0
  - fastapi-cli[standard]>=0.0.5 ; extra == 'standard'
  - httpx>=0.23.0 ; extra == 'standard'
  - jinja2>=3.1.5 ; extra == 'standard'
  - python-multipart>=0.0.18 ; extra == 'standard'
  - email-validator>=2.0.0 ; extra == 'standard'
  - uvicorn[standard]>=0.12.0 ; extra == 'standard'
  - fastapi-cli[standard]>=0.0.5 ; extra == 'all'
  - httpx>=0.23.0 ; extra == 'all'
  - jinja2>=3.1.5 ; extra == 'all'
  - python-multipart>=0.0.18 ; extra == 'all'
  - itsdangerous>=1.1.0 ; extra == 'all'
  - pyyaml>=5.3.1 ; extra == 'all'
  - ujson>=4.0.1,!=4.0.2,!=4.1.0,!=4.2.0,!=4.3.0,!=5.0.0,!=5.1.0 ; extra == 'all'
  - orjson>=3.2.1 ; extra == 'all'
  - email-validator>=2.0.0 ; extra == 'all'
  - uvicorn[standard]>=0.12.0 ; extra == 'all'
  - pydantic-settings>=2.0.0 ; extra == 'all'
  - pydantic-extra-types>=2.0.0 ; extra == 'all'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/0e/e2/b066e6e02d67bf5261a6d7539648c6da3365cc9eff3eb6d82009595d84d9/flatbuffers-25.1.24-py2.py3-none-any.whl
  name: flatbuffers
  version: 25.1.24
  sha256: 1abfebaf4083117225d0723087ea909896a34e3fec933beedb490d595ba24145
- pypi: https://files.pythonhosted.org/packages/af/f2/64b73a9bb86f5a89fb55450e97cd5c1f84a862d4ff90d9fd1a73ab0f64a5/frozenlist-1.5.0-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: frozenlist
  version: 1.5.0
  sha256: 000a77d6034fbad9b6bb880f7ec073027908f1b40254b5d6f26210d2dab1240e
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/b1/56/4e45136ffc6bdbfa68c29ca56ef53783ef4c2fd395f7cbf99a2624aa9aaa/frozenlist-1.5.0-cp312-cp312-win_amd64.whl
  name: frozenlist
  version: 1.5.0
  sha256: 8969190d709e7c48ea386db202d708eb94bdb29207a1f269bab1196ce0dcca1f
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/b0/6c/5a53d632b44ef7655ac8d9b34432e13160917f9307c94b1467efd34e336e/groq-0.18.0-py3-none-any.whl
  name: groq
  version: 0.18.0
  sha256: 81d5ac00057a45d8ce559d23ab5d3b3893011d1f12c35187ab35a9182d826ea6
  requires_dist:
  - anyio>=3.5.0,<5
  - distro>=1.7.0,<2
  - httpx>=0.23.0,<1
  - pydantic>=1.9.0,<3
  - sniffio
  - typing-extensions>=4.10,<5
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/95/04/ff642e65ad6b90db43e668d70ffb6736436c7ce41fcc549f4e9472234127/h11-0.14.0-py3-none-any.whl
  name: h11
  version: 0.14.0
  sha256: e3fe4ac4b851c468cc8363d500db52c2ead036020723024a109d37346efaa761
  requires_dist:
  - typing-extensions ; python_full_version < '3.8'
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/87/f5/72347bc88306acb359581ac4d52f23c0ef445b57157adedb9aee0cd689d2/httpcore-1.0.7-py3-none-any.whl
  name: httpcore
  version: 1.0.7
  sha256: a3fff8f43dc260d5bd363d9f9cf1830fa3a458b332856f34282de498ed420edd
  requires_dist:
  - certifi
  - h11>=0.13,<0.15
  - anyio>=4.0,<5.0 ; extra == 'asyncio'
  - h2>=3,<5 ; extra == 'http2'
  - socksio==1.* ; extra == 'socks'
  - trio>=0.22.0,<1.0 ; extra == 'trio'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/12/b7/5cae71a8868e555f3f67a50ee7f673ce36eac970f029c0c5e9d584352961/httptools-0.6.4-cp312-cp312-win_amd64.whl
  name: httptools
  version: 0.6.4
  sha256: db78cb9ca56b59b016e64b6031eda5653be0589dba2b1b43453f6e8b405a0970
  requires_dist:
  - cython>=0.29.24 ; extra == 'test'
  requires_python: '>=3.8.0'
- pypi: https://files.pythonhosted.org/packages/f7/d8/b644c44acc1368938317d76ac991c9bba1166311880bcc0ac297cb9d6bd7/httptools-0.6.4-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: httptools
  version: 0.6.4
  sha256: 16e603a3bff50db08cd578d54f07032ca1631450ceb972c2f834c2b860c28ea2
  requires_dist:
  - cython>=0.29.24 ; extra == 'test'
  requires_python: '>=3.8.0'
- pypi: https://files.pythonhosted.org/packages/2a/39/e50c7c3a983047577ee07d2a9e53faf5a69493943ec3f6a384bdc792deb2/httpx-0.28.1-py3-none-any.whl
  name: httpx
  version: 0.28.1
  sha256: d909fcccc110f8c7faf814ca82a9a4d816bc5a6dbfea25d6591d6985b8ba59ad
  requires_dist:
  - anyio
  - certifi
  - httpcore==1.*
  - idna
  - brotli ; platform_python_implementation == 'CPython' and extra == 'brotli'
  - brotlicffi ; platform_python_implementation != 'CPython' and extra == 'brotli'
  - click==8.* ; extra == 'cli'
  - pygments==2.* ; extra == 'cli'
  - rich>=10,<14 ; extra == 'cli'
  - h2>=3,<5 ; extra == 'http2'
  - socksio==1.* ; extra == 'socks'
  - zstandard>=0.18.0 ; extra == 'zstd'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/f0/0f/310fb31e39e2d734ccaa2c0fb981ee41f7bd5056ce9bc29b2248bd569169/humanfriendly-10.0-py2.py3-none-any.whl
  name: humanfriendly
  version: '10.0'
  sha256: 1697e1a8a8f550fd43c2865cd84542fc175a61dcb779b6fee18cf6b6ccba1477
  requires_dist:
  - monotonic ; python_full_version == '2.7.*'
  - pyreadline ; python_full_version < '3.8' and sys_platform == 'win32'
  - pyreadline3 ; python_full_version >= '3.8' and sys_platform == 'win32'
  requires_python: '>=2.7,!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*,!=3.4.*'
- pypi: https://files.pythonhosted.org/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl
  name: idna
  version: '3.10'
  sha256: 946d195a0d259cbba61165e88e65941f16e9b36ea6ddb97f00452bae8b1287d3
  requires_dist:
  - ruff>=0.6.2 ; extra == 'all'
  - mypy>=1.11.2 ; extra == 'all'
  - pytest>=8.3.2 ; extra == 'all'
  - flake8>=7.1.1 ; extra == 'all'
  requires_python: '>=3.6'
- pypi: https://files.pythonhosted.org/packages/17/61/beea645c0bf398ced8b199e377b61eb999d8e46e053bb285c91c3d3eaab0/jiter-0.8.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: jiter
  version: 0.8.2
  sha256: 14601dcac4889e0a1c75ccf6a0e4baf70dbc75041e51bcf8d0e9274519df6887
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/41/69/6d4bbe66b3b3b4507e47aa1dd5d075919ad242b4b1115b3f80eecd443687/jiter-0.8.2-cp312-cp312-win_amd64.whl
  name: jiter
  version: 0.8.2
  sha256: 83c0efd80b29695058d0fd2fa8a556490dbce9804eac3e281f373bbc99045f6c
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/0e/72/a3add0e4eec4eb9e2569554f7c70f4a3c27712f40e3284d483e88094cc0e/langdetect-1.0.9.tar.gz
  name: langdetect
  version: 1.0.9
  sha256: cbc1fef89f8d062739774bd51eda3da3274006b3661d199c2655f6b3f6d605a0
  requires_dist:
  - six
- conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.43-h712a8e2_2.conda
  sha256: 7c91cea91b13f4314d125d1bedb9d03a29ebbd5080ccdea70260363424646dbe
  md5: 048b02e3962f066da18efe3a21b77672
  depends:
  - __glibc >=2.17,<3.0.a0
  constrains:
  - binutils_impl_linux-64 2.43
  license: GPL-3.0-only
  license_family: GPL
  purls: []
  size: 669211
  timestamp: 1729655358674
- conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.6.4-h5888daf_0.conda
  sha256: 56541b98447b58e52d824bd59d6382d609e11de1f8adf20b23143e353d2b8d26
  md5: db833e03127376d461e1e13e76f09b6c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - expat 2.6.4.*
  license: MIT
  license_family: MIT
  purls: []
  size: 73304
  timestamp: 1730967041968
- conda: https://conda.anaconda.org/conda-forge/win-64/libexpat-2.6.4-he0c23c2_0.conda
  sha256: 0c0447bf20d1013d5603499de93a16b6faa92d7ead870d96305c0f065b6a5a12
  md5: eb383771c680aa792feb529eaf9df82f
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  constrains:
  - expat 2.6.4.*
  license: MIT
  license_family: MIT
  purls: []
  size: 139068
  timestamp: 1730967442102
- conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.2-h7f98852_5.tar.bz2
  sha256: ab6e9856c21709b7b517e940ae7028ae0737546122f83c2aa5d692860c3b149e
  md5: d645c6d2ac96843a2bfaccd2d62b3ac3
  depends:
  - libgcc-ng >=9.4.0
  license: MIT
  license_family: MIT
  purls: []
  size: 58292
  timestamp: 1636488182923
- conda: https://conda.anaconda.org/conda-forge/win-64/libffi-3.4.2-h8ffe710_5.tar.bz2
  sha256: 1951ab740f80660e9bc07d2ed3aefb874d78c107264fd810f24a1a6211d4b1a5
  md5: 2c96d1b6915b408893f9472569dee135
  depends:
  - vc >=14.1,<15.0a0
  - vs2015_runtime >=14.16.27012
  license: MIT
  license_family: MIT
  purls: []
  size: 42063
  timestamp: 1636489106777
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-14.2.0-h77fa898_1.conda
  sha256: 53eb8a79365e58849e7b1a068d31f4f9e718dc938d6f2c03e960345739a03569
  md5: 3cb76c3f10d3bc7f1105b2fc9db984df
  depends:
  - _libgcc_mutex 0.1 conda_forge
  - _openmp_mutex >=4.5
  constrains:
  - libgomp 14.2.0 h77fa898_1
  - libgcc-ng ==14.2.0=*_1
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 848745
  timestamp: 1729027721139
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-14.2.0-h69a702a_1.conda
  sha256: 3a76969c80e9af8b6e7a55090088bc41da4cffcde9e2c71b17f44d37b7cb87f7
  md5: e39480b9ca41323497b05492a63bc35b
  depends:
  - libgcc 14.2.0 h77fa898_1
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 54142
  timestamp: 1729027726517
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-14.2.0-h77fa898_1.conda
  sha256: 1911c29975ec99b6b906904040c855772ccb265a1c79d5d75c8ceec4ed89cd63
  md5: cc3573974587f12dda90d96e3e55a702
  depends:
  - _libgcc_mutex 0.1 conda_forge
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 460992
  timestamp: 1729027639220
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.6.4-hb9d3cd8_0.conda
  sha256: cad52e10319ca4585bc37f0bc7cce99ec7c15dc9168e42ccb96b741b0a27db3f
  md5: 42d5b6a0f30d3c10cd88cb8584fda1cb
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: 0BSD
  purls: []
  size: 111357
  timestamp: 1738525339684
- conda: https://conda.anaconda.org/conda-forge/win-64/liblzma-5.6.4-h2466b09_0.conda
  sha256: 3f552b0bdefdd1459ffc827ea3bf70a6a6920c7879d22b6bfd0d73015b55227b
  md5: c48f6ad0ef0a555b27b233dfcab46a90
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: 0BSD
  purls: []
  size: 104465
  timestamp: 1738525557254
- conda: https://conda.anaconda.org/conda-forge/linux-64/libnsl-2.0.1-hd590300_0.conda
  sha256: 26d77a3bb4dceeedc2a41bd688564fe71bf2d149fdcf117049970bc02ff1add6
  md5: 30fd6e37fe21f86f4bd26d6ee73eeec7
  depends:
  - libgcc-ng >=12
  license: LGPL-2.1-only
  license_family: GPL
  purls: []
  size: 33408
  timestamp: 1697359010159
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.49.0-hee588c1_0.conda
  sha256: 51c68794a1f32c2742cbcfc8ea8cd730bb19148bed437197380778d3a6d49385
  md5: a12aa55f2a4446af8aa44d69ac563d58
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: Unlicense
  purls: []
  size: 917347
  timestamp: 1739176276854
- conda: https://conda.anaconda.org/conda-forge/win-64/libsqlite-3.49.0-h67fdade_0.conda
  sha256: ff6670afcef19234145cd7e2e3b97a6a591a09f2b98d0920b140bf175979db47
  md5: b5aae7f19da05cb648ca214bf14ddd81
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: Unlicense
  purls: []
  size: 1082337
  timestamp: 1739176678486
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-14.2.0-hc0a3c3a_1.conda
  sha256: 4661af0eb9bdcbb5fb33e5d0023b001ad4be828fccdcc56500059d56f9869462
  md5: 234a5554c53625688d51062645337328
  depends:
  - libgcc 14.2.0 h77fa898_1
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 3893695
  timestamp: 1729027746910
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-14.2.0-h4852527_1.conda
  sha256: 25bb30b827d4f6d6f0522cc0579e431695503822f144043b93c50237017fffd8
  md5: 8371ac6457591af2cf6159439c1fd051
  depends:
  - libstdcxx 14.2.0 hc0a3c3a_1
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 54105
  timestamp: 1729027780628
- conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
  sha256: 787eb542f055a2b3de553614b25f09eefb0a0931b0c87dbcce6efdfd92f04f18
  md5: 40b61aab5c7ba9ff276c41cfffe6b80b
  depends:
  - libgcc-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 33601
  timestamp: 1680112270483
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxcrypt-4.4.36-hd590300_1.conda
  sha256: 6ae68e0b86423ef188196fff6207ed0c8195dd84273cb5623b85aa08033a410c
  md5: 5aa797f8787fe7a17d1b0821485b5adc
  depends:
  - libgcc-ng >=12
  license: LGPL-2.1-or-later
  purls: []
  size: 100393
  timestamp: 1702724383534
- conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
  sha256: d4bfe88d7cb447768e31650f06257995601f89076080e76df55e3112d4e47dc4
  md5: edb0dca6bc32e4f4789199455a1dbeb8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  purls: []
  size: 60963
  timestamp: 1727963148474
- conda: https://conda.anaconda.org/conda-forge/win-64/libzlib-1.3.1-h2466b09_2.conda
  sha256: ba945c6493449bed0e6e29883c4943817f7c79cbff52b83360f7b341277c6402
  md5: 41fbfac52c601159df6c01f875de31b9
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  purls: []
  size: 55476
  timestamp: 1727963768015
- conda: https://conda.anaconda.org/conda-forge/win-64/libzlib-wapi-1.2.13-h2466b09_6.conda
  sha256: dd92ecd1f39e17623fd6149cf0dc7f675d2f9e091ef2b1774a85e9e545434102
  md5: 84ac5ada002445227139f8f659cf6d93
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  constrains:
  - zlib-wapi 1.2.13 *_6
  - zlib 1.2.13 *_6
  license: Zlib
  license_family: Other
  purls: []
  size: 56048
  timestamp: 1716874638604
- pypi: https://files.pythonhosted.org/packages/0c/29/0348de65b8cc732daa3e33e67806420b2ae89bdce2b04af740289c5c6c8c/loguru-0.7.3-py3-none-any.whl
  name: loguru
  version: 0.7.3
  sha256: 31a33c10c8e1e10422bfd431aeb5d351c7cf7fa671e3c4df004162264b28220c
  requires_dist:
  - colorama>=0.3.4 ; sys_platform == 'win32'
  - aiocontextvars>=0.2.0 ; python_full_version < '3.7'
  - win32-setctime>=1.0.0 ; sys_platform == 'win32'
  - pre-commit==4.0.1 ; python_full_version >= '3.9' and extra == 'dev'
  - tox==3.27.1 ; python_full_version < '3.8' and extra == 'dev'
  - tox==4.23.2 ; python_full_version >= '3.8' and extra == 'dev'
  - pytest==6.1.2 ; python_full_version < '3.8' and extra == 'dev'
  - pytest==8.3.2 ; python_full_version >= '3.8' and extra == 'dev'
  - pytest-cov==2.12.1 ; python_full_version < '3.8' and extra == 'dev'
  - pytest-cov==5.0.0 ; python_full_version == '3.8.*' and extra == 'dev'
  - pytest-cov==6.0.0 ; python_full_version >= '3.9' and extra == 'dev'
  - pytest-mypy-plugins==1.9.3 ; python_full_version >= '3.6' and python_full_version < '3.8' and extra == 'dev'
  - pytest-mypy-plugins==3.1.0 ; python_full_version >= '3.8' and extra == 'dev'
  - colorama==0.4.5 ; python_full_version < '3.8' and extra == 'dev'
  - colorama==0.4.6 ; python_full_version >= '3.8' and extra == 'dev'
  - freezegun==1.1.0 ; python_full_version < '3.8' and extra == 'dev'
  - freezegun==1.5.0 ; python_full_version >= '3.8' and extra == 'dev'
  - exceptiongroup==1.1.3 ; python_full_version >= '3.7' and python_full_version < '3.11' and extra == 'dev'
  - mypy==0.910 ; python_full_version < '3.6' and extra == 'dev'
  - mypy==0.971 ; python_full_version == '3.6.*' and extra == 'dev'
  - mypy==1.4.1 ; python_full_version == '3.7.*' and extra == 'dev'
  - mypy==1.13.0 ; python_full_version >= '3.8' and extra == 'dev'
  - sphinx==8.1.3 ; python_full_version >= '3.11' and extra == 'dev'
  - sphinx-rtd-theme==3.0.2 ; python_full_version >= '3.11' and extra == 'dev'
  - myst-parser==4.0.0 ; python_full_version >= '3.11' and extra == 'dev'
  - build==1.2.2 ; python_full_version >= '3.11' and extra == 'dev'
  - twine==6.0.1 ; python_full_version >= '3.11' and extra == 'dev'
  requires_python: '>=3.5,<4.0'
- pypi: https://files.pythonhosted.org/packages/43/e3/7d92a15f894aa0c9c4b49b8ee9ac9850d6e63b03c9c32c0367a13ae62209/mpmath-1.3.0-py3-none-any.whl
  name: mpmath
  version: 1.3.0
  sha256: a0b2b9fe80bbcd81a6647ff13108738cfb482d481d826cc0e02f5b35e5c88d2c
  requires_dist:
  - pytest>=4.6 ; extra == 'develop'
  - pycodestyle ; extra == 'develop'
  - pytest-cov ; extra == 'develop'
  - codecov ; extra == 'develop'
  - wheel ; extra == 'develop'
  - sphinx ; extra == 'docs'
  - gmpy2>=2.1.0a4 ; platform_python_implementation != 'PyPy' and extra == 'gmpy'
  - pytest>=4.6 ; extra == 'tests'
- pypi: https://files.pythonhosted.org/packages/a3/bf/f332a13486b1ed0496d624bcc7e8357bb8053823e8cd4b9a18edc1d97e73/multidict-6.1.0-cp312-cp312-win_amd64.whl
  name: multidict
  version: 6.1.0
  sha256: 188215fc0aafb8e03341995e7c4797860181562380f81ed0a87ff455b70bf1f1
  requires_dist:
  - typing-extensions>=4.1.0 ; python_full_version < '3.11'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/d3/c8/529101d7176fe7dfe1d99604e48d69c5dfdcadb4f06561f465c8ef12b4df/multidict-6.1.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: multidict
  version: 6.1.0
  sha256: 4b820514bfc0b98a30e3d85462084779900347e4d49267f747ff54060cc33925
  requires_dist:
  - typing-extensions>=4.1.0 ; python_full_version < '3.11'
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
  sha256: 3fde293232fa3fca98635e1167de6b7c7fda83caf24b9d6c91ec9eefb4f4d586
  md5: 47e340acb35de30501a76c7c799c41d7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: X11 AND BSD-3-Clause
  purls: []
  size: 891641
  timestamp: 1738195959188
- pypi: https://files.pythonhosted.org/packages/0f/50/de23fde84e45f5c4fda2488c759b69990fd4512387a8632860f3ac9cd225/numpy-1.26.4-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: numpy
  version: 1.26.4
  sha256: 675d61ffbfa78604709862923189bad94014bef562cc35cf61d3a07bba02a7ed
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/16/2e/86f24451c2d530c88daf997cb8d6ac622c1d40d19f5a031ed68a4b73a374/numpy-1.26.4-cp312-cp312-win_amd64.whl
  name: numpy
  version: 1.26.4
  sha256: 08beddf13648eb95f8d867350f6a018a4be2e5ad54c8d8caed89ebca558b2818
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/47/42/2f71f5680834688a9c81becbe5c5bb996fd33eaed5c66ae0606c3b1d6a02/onnxruntime-1.20.1-cp312-cp312-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl
  name: onnxruntime
  version: 1.20.1
  sha256: bb71a814f66517a65628c9e4a2bb530a6edd2cd5d87ffa0af0f6f773a027d99e
  requires_dist:
  - coloredlogs
  - flatbuffers
  - numpy>=1.21.6
  - packaging
  - protobuf
  - sympy
- pypi: https://files.pythonhosted.org/packages/dd/80/76979e0b744307d488c79e41051117634b956612cc731f1028eb17ee7294/onnxruntime-1.20.1-cp312-cp312-win_amd64.whl
  name: onnxruntime
  version: 1.20.1
  sha256: 19c2d843eb074f385e8bbb753a40df780511061a63f9def1b216bf53860223fb
  requires_dist:
  - coloredlogs
  - flatbuffers
  - numpy>=1.21.6
  - packaging
  - protobuf
  - sympy
- pypi: .
  name: open-llm-vtuber
  version: 1.0.3
  sha256: 006c9a1b22f28c2ed3da9a98b45007a1b13afdb34bc66a2a780355a2a6ae55d3
  requires_dist:
  - anthropic>=0.40.0
  - azure-cognitiveservices-speech>=1.41.1
  - chardet>=5.2.0
  - edge-tts>=7.0.0
  - fastapi>=0.115.6
  - groq>=0.13.0
  - httpx>=0.28.1
  - langdetect>=1.0.9
  - loguru>=0.7.2
  - numpy>=1.26.4,<2
  - onnxruntime>=1.20.1
  - openai>=1.57.4
  - pydub>=0.25.1
  - pysbd>=0.3.4
  - pyttsx3>=2.98
  - pyyaml>=6.0.2
  - requests>=2.32.3
  - ruff>=0.8.6
  - scipy>=1.14.1
  - sherpa-onnx>=1.10.39
  - soundfile>=0.12.1
  - tomli>=2.2.1
  - tqdm>=4.67.1
  - uvicorn[standard]>=0.33.0
  - websocket-client>=1.8.0
  requires_python: '>=3.10,<3.13'
  editable: true
- pypi: https://files.pythonhosted.org/packages/9a/b6/2e2a011b2dc27a6711376808b4cd8c922c476ea0f1420b39892117fa8563/openai-1.61.1-py3-none-any.whl
  name: openai
  version: 1.61.1
  sha256: 72b0826240ce26026ac2cd17951691f046e5be82ad122d20a8e1b30ca18bd11e
  requires_dist:
  - anyio>=3.5.0,<5
  - distro>=1.7.0,<2
  - httpx>=0.23.0,<1
  - jiter>=0.4.0,<1
  - pydantic>=1.9.0,<3
  - sniffio
  - tqdm>4
  - typing-extensions>=4.11,<5
  - numpy>=1 ; extra == 'datalib'
  - pandas-stubs>=******** ; extra == 'datalib'
  - pandas>=1.2.3 ; extra == 'datalib'
  - websockets>=13,<15 ; extra == 'realtime'
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.4.0-h7b32b05_1.conda
  sha256: f62f6bca4a33ca5109b6d571b052a394d836956d21b25b7ffd03376abf7a481f
  md5: 4ce6875f75469b2757a65e10a5d05e31
  depends:
  - __glibc >=2.17,<3.0.a0
  - ca-certificates
  - libgcc >=13
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 2937158
  timestamp: 1736086387286
- conda: https://conda.anaconda.org/conda-forge/win-64/openssl-3.4.0-ha4e3fda_1.conda
  sha256: 519a06eaab7c878fbebb8cab98ea4a4465eafb1e9ed8c6ce67226068a80a92f0
  md5: fb45308ba8bfe1abf1f4a27bad24a743
  depends:
  - ca-certificates
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 8462960
  timestamp: 1736088436984
- pypi: https://files.pythonhosted.org/packages/88/ef/eb23f262cca3c0c4eb7ab1933c3b1f03d021f2c48f54763065b6f0e321be/packaging-24.2-py3-none-any.whl
  name: packaging
  version: '24.2'
  sha256: 09abb1bccd265c01f4a3aa3f7a7db064b36514d2cba19a2f694fe6150451a759
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/1c/07/ebe102777a830bca91bbb93e3479cd34c2ca5d0361b83be9dbd93104865e/propcache-0.2.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: propcache
  version: 0.2.1
  sha256: 647894f5ae99c4cf6bb82a1bb3a796f6e06af3caa3d32e26d2350d0e3e3faf24
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/3b/77/a92c3ef994e47180862b9d7d11e37624fb1c00a16d61faf55115d970628b/propcache-0.2.1-cp312-cp312-win_amd64.whl
  name: propcache
  version: 0.2.1
  sha256: c214999039d4f2a5b2073ac506bba279945233da8c786e490d411dfc30f855c1
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/61/fa/aae8e10512b83de633f2646506a6d835b151edf4b30d18d73afd01447253/protobuf-5.29.3-cp310-abi3-win_amd64.whl
  name: protobuf
  version: 5.29.3
  sha256: a4fa6f80816a9a0678429e84973f2f98cbc218cca434abe8db2ad0bffc98503a
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/a8/45/2ebbde52ad2be18d3675b6bee50e68cd73c9e0654de77d595540b5129df8/protobuf-5.29.3-cp38-abi3-manylinux2014_x86_64.whl
  name: protobuf
  version: 5.29.3
  sha256: c027e08a08be10b67c06bf2370b99c811c466398c357e615ca88c91c07f0910f
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/13/a3/a812df4e2dd5696d1f351d58b8fe16a405b234ad2886a0dab9183fb78109/pycparser-2.22-py3-none-any.whl
  name: pycparser
  version: '2.22'
  sha256: c3702b6d3dd8c7abc1afa565d7e63d53a1d0bd86cdc24edd75470f4de499cfcc
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/f4/3c/8cc1cc84deffa6e25d2d0c688ebb80635dfdbf1dbea3e30c541c8cf4d860/pydantic-2.10.6-py3-none-any.whl
  name: pydantic
  version: 2.10.6
  sha256: 427d664bf0b8a2b34ff5dd0f5a18df00591adcee7198fbd71981054cef37b584
  requires_dist:
  - annotated-types>=0.6.0
  - pydantic-core==2.27.2
  - typing-extensions>=4.12.2
  - email-validator>=2.0.0 ; extra == 'email'
  - tzdata ; python_full_version >= '3.9' and platform_system == 'Windows' and extra == 'timezone'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/1f/ea/cd7209a889163b8dcca139fe32b9687dd05249161a3edda62860430457a5/pydantic_core-2.27.2-cp312-cp312-win_amd64.whl
  name: pydantic-core
  version: 2.27.2
  sha256: cc3f1a99a4f4f9dd1de4fe0312c114e740b5ddead65bb4102884b384c15d8bc9
  requires_dist:
  - typing-extensions>=4.6.0,!=4.7.0
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/8d/f0/49129b27c43396581a635d8710dae54a791b17dfc50c70164866bbf865e3/pydantic_core-2.27.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: pydantic-core
  version: 2.27.2
  sha256: 6fb4aadc0b9a0c063206846d603b92030eb6f03069151a625667f982887153e2
  requires_dist:
  - typing-extensions>=4.6.0,!=4.7.0
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/a6/53/d78dc063216e62fc55f6b2eebb447f6a4b0a59f55c8406376f76bf959b08/pydub-0.25.1-py2.py3-none-any.whl
  name: pydub
  version: 0.25.1
  sha256: 65617e33033874b59d87db603aa1ed450633288aefead953b30bded59cb599a6
- pypi: https://files.pythonhosted.org/packages/d0/1b/2f292bbd742e369a100c91faa0483172cd91a1a422a6692055ac920946c5/pypiwin32-223-py3-none-any.whl
  name: pypiwin32
  version: '223'
  sha256: 67adf399debc1d5d14dffc1ab5acacb800da569754fafdc576b2a039485aa775
  requires_dist:
  - pywin32>=223
- pypi: https://files.pythonhosted.org/packages/5a/dc/491b7661614ab97483abf2056be1deee4dc2490ecbf7bff9ab5cdbac86e1/pyreadline3-3.5.4-py3-none-any.whl
  name: pyreadline3
  version: 3.5.4
  sha256: eaf8e6cc3c49bcccf145fc6067ba8643d1df34d604a1ec0eccbf7a18e6d3fae6
  requires_dist:
  - build ; extra == 'dev'
  - flake8 ; extra == 'dev'
  - mypy ; extra == 'dev'
  - pytest ; extra == 'dev'
  - twine ; extra == 'dev'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/48/0a/c99fb7d7e176f8b176ef19704a32e6a9c6aafdf19ef75a187f701fc15801/pysbd-0.3.4-py3-none-any.whl
  name: pysbd
  version: 0.3.4
  sha256: cd838939b7b0b185fcf86b0baf6636667dfb6e474743beeff878e9f42e022953
  requires_python: '>=3'
- conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.12.8-h9e4cc4f_1_cpython.conda
  build_number: 1
  sha256: 3f0e0518c992d8ccfe62b189125721309836fe48a010dc424240583e157f9ff0
  md5: 7fd2fd79436d9b473812f14e86746844
  depends:
  - __glibc >=2.17,<3.0.a0
  - bzip2 >=1.0.8,<2.0a0
  - ld_impl_linux-64 >=2.36.1
  - libexpat >=2.6.4,<3.0a0
  - libffi >=3.4,<4.0a0
  - libgcc >=13
  - liblzma >=5.6.3,<6.0a0
  - libnsl >=2.0.1,<2.1.0a0
  - libsqlite >=3.47.0,<4.0a0
  - libuuid >=2.38.1,<3.0a0
  - libxcrypt >=4.4.36
  - libzlib >=1.3.1,<2.0a0
  - ncurses >=6.5,<7.0a0
  - openssl >=3.4.0,<4.0a0
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  constrains:
  - python_abi 3.12.* *_cp312
  license: Python-2.0
  purls: []
  size: 31565686
  timestamp: 1733410597922
- conda: https://conda.anaconda.org/conda-forge/win-64/python-3.12.8-h3f84c4b_1_cpython.conda
  build_number: 1
  sha256: e1b37a398b3e2ea363de7cff6706e5ec2a5eb36b211132150e8601d7afd8f3aa
  md5: 8cd0693344796fb32087185fca16f4cc
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - libexpat >=2.6.4,<3.0a0
  - libffi >=3.4,<4.0a0
  - liblzma >=5.6.3,<6.0a0
  - libsqlite >=3.47.0,<4.0a0
  - libzlib >=1.3.1,<2.0a0
  - openssl >=3.4.0,<4.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  constrains:
  - python_abi 3.12.* *_cp312
  license: Python-2.0
  purls: []
  size: 15812363
  timestamp: 1733408080064
- pypi: https://files.pythonhosted.org/packages/6a/3e/b68c118422ec867fa7ab88444e1274aa40681c606d59ac27de5a5588f082/python_dotenv-1.0.1-py3-none-any.whl
  name: python-dotenv
  version: 1.0.1
  sha256: f7b63ef50f1b690dddf550d03497b66d609393b40b564ed0d674909a68ebf16a
  requires_dist:
  - click>=5.0 ; extra == 'cli'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/94/df/e1584757c736c4fba09a3fb4f22fe625cc3367b06c6ece221e4b8c1e3023/pyttsx3-2.98-py3-none-any.whl
  name: pyttsx3
  version: '2.98'
  sha256: b3fb4ca4d5ae4f8e6836d6b37bf5fee0fd51d157ffa27fb9064be6e7be3da37a
  requires_dist:
  - pyobjc>=2.4 ; platform_system == 'Darwin'
  - comtypes ; platform_system == 'Windows'
  - pypiwin32 ; platform_system == 'Windows'
  - pywin32 ; platform_system == 'Windows'
- pypi: https://files.pythonhosted.org/packages/21/27/0c8811fbc3ca188f93b5354e7c286eb91f80a53afa4e11007ef661afa746/pywin32-308-cp312-cp312-win_amd64.whl
  name: pywin32
  version: '308'
  sha256: 00b3e11ef09ede56c6a43c71f2d31857cf7c54b0ab6e78ac659497abd2834f47
- pypi: https://files.pythonhosted.org/packages/0c/e8/4f648c598b17c3d06e8753d7d13d57542b30d56e6c2dedf9c331ae56312e/PyYAML-6.0.2-cp312-cp312-win_amd64.whl
  name: pyyaml
  version: 6.0.2
  sha256: 7e7401d0de89a9a855c839bc697c079a4af81cf878373abd7dc625847d25cbd8
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/b9/2b/614b4752f2e127db5cc206abc23a8c19678e92b23c3db30fc86ab731d3bd/PyYAML-6.0.2-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: pyyaml
  version: 6.0.2
  sha256: 80bab7bfc629882493af4aa31a4cfa43a4c57c83813253626916b8c7ada83476
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8228510_1.conda
  sha256: 5435cf39d039387fbdc977b0a762357ea909a7694d9528ab40f005e9208744d7
  md5: 47d31b792659ce70f470b5c82fdfb7a4
  depends:
  - libgcc-ng >=12
  - ncurses >=6.3,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  purls: []
  size: 281456
  timestamp: 1679532220005
- pypi: https://files.pythonhosted.org/packages/f9/9b/335f9764261e915ed497fcdeb11df5dfd6f7bf257d4a6a2a686d80da4d54/requests-2.32.3-py3-none-any.whl
  name: requests
  version: 2.32.3
  sha256: 70761cfe03c773ceb22aa2f671b4757976145175cdfca038c02654d061d6dcc6
  requires_dist:
  - charset-normalizer>=2,<4
  - idna>=2.5,<4
  - urllib3>=1.21.1,<3
  - certifi>=2017.4.17
  - pysocks>=1.5.6,!=1.5.7 ; extra == 'socks'
  - chardet>=3.0.2,<6 ; extra == 'use-chardet-on-py3'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/04/70/e59c192a3ad476355e7f45fb3a87326f5219cc7c472e6b040c6c6595c8f0/ruff-0.9.5-py3-none-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: ruff
  version: 0.9.5
  sha256: 2c746d7d1df64f31d90503ece5cc34d7007c06751a7a3bbeee10e5f2463d52d2
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/b7/ad/c7a900591bd152bb47fc4882a27654ea55c7973e6d5d6396298ad3fd6638/ruff-0.9.5-py3-none-win_amd64.whl
  name: ruff
  version: 0.9.5
  sha256: 78cc6067f6d80b6745b67498fb84e87d32c6fc34992b52bffefbdae3442967d6
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/b0/3c/0de11ca154e24a57b579fb648151d901326d3102115bc4f9a7a86526ce54/scipy-1.15.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: scipy
  version: 1.15.1
  sha256: 0fb57b30f0017d4afa5fe5f5b150b8f807618819287c21cbe51130de7ccdaed2
  requires_dist:
  - numpy>=1.23.5,<2.5
  - pytest ; extra == 'test'
  - pytest-cov ; extra == 'test'
  - pytest-timeout ; extra == 'test'
  - pytest-xdist ; extra == 'test'
  - asv ; extra == 'test'
  - mpmath ; extra == 'test'
  - gmpy2 ; extra == 'test'
  - threadpoolctl ; extra == 'test'
  - scikit-umfpack ; extra == 'test'
  - pooch ; extra == 'test'
  - hypothesis>=6.30 ; extra == 'test'
  - array-api-strict>=2.0,<2.1.1 ; extra == 'test'
  - cython ; extra == 'test'
  - meson ; extra == 'test'
  - ninja ; sys_platform != 'emscripten' and extra == 'test'
  - sphinx>=5.0.0,<8.0.0 ; extra == 'doc'
  - intersphinx-registry ; extra == 'doc'
  - pydata-sphinx-theme>=0.15.2 ; extra == 'doc'
  - sphinx-copybutton ; extra == 'doc'
  - sphinx-design>=0.4.0 ; extra == 'doc'
  - matplotlib>=3.5 ; extra == 'doc'
  - numpydoc ; extra == 'doc'
  - jupytext ; extra == 'doc'
  - myst-nb ; extra == 'doc'
  - pooch ; extra == 'doc'
  - jupyterlite-sphinx>=0.16.5 ; extra == 'doc'
  - jupyterlite-pyodide-kernel ; extra == 'doc'
  - mypy==1.10.0 ; extra == 'dev'
  - typing-extensions ; extra == 'dev'
  - types-psutil ; extra == 'dev'
  - pycodestyle ; extra == 'dev'
  - ruff>=0.0.292 ; extra == 'dev'
  - cython-lint>=0.12.2 ; extra == 'dev'
  - rich-click ; extra == 'dev'
  - doit>=0.36.0 ; extra == 'dev'
  - pydevtool ; extra == 'dev'
  requires_python: '>=3.10'
- pypi: https://files.pythonhosted.org/packages/ff/ba/31c7a8131152822b3a2cdeba76398ffb404d81d640de98287d236da90c49/scipy-1.15.1-cp312-cp312-win_amd64.whl
  name: scipy
  version: 1.15.1
  sha256: 900f3fa3db87257510f011c292a5779eb627043dd89731b9c461cd16ef76ab3d
  requires_dist:
  - numpy>=1.23.5,<2.5
  - pytest ; extra == 'test'
  - pytest-cov ; extra == 'test'
  - pytest-timeout ; extra == 'test'
  - pytest-xdist ; extra == 'test'
  - asv ; extra == 'test'
  - mpmath ; extra == 'test'
  - gmpy2 ; extra == 'test'
  - threadpoolctl ; extra == 'test'
  - scikit-umfpack ; extra == 'test'
  - pooch ; extra == 'test'
  - hypothesis>=6.30 ; extra == 'test'
  - array-api-strict>=2.0,<2.1.1 ; extra == 'test'
  - cython ; extra == 'test'
  - meson ; extra == 'test'
  - ninja ; sys_platform != 'emscripten' and extra == 'test'
  - sphinx>=5.0.0,<8.0.0 ; extra == 'doc'
  - intersphinx-registry ; extra == 'doc'
  - pydata-sphinx-theme>=0.15.2 ; extra == 'doc'
  - sphinx-copybutton ; extra == 'doc'
  - sphinx-design>=0.4.0 ; extra == 'doc'
  - matplotlib>=3.5 ; extra == 'doc'
  - numpydoc ; extra == 'doc'
  - jupytext ; extra == 'doc'
  - myst-nb ; extra == 'doc'
  - pooch ; extra == 'doc'
  - jupyterlite-sphinx>=0.16.5 ; extra == 'doc'
  - jupyterlite-pyodide-kernel ; extra == 'doc'
  - mypy==1.10.0 ; extra == 'dev'
  - typing-extensions ; extra == 'dev'
  - types-psutil ; extra == 'dev'
  - pycodestyle ; extra == 'dev'
  - ruff>=0.0.292 ; extra == 'dev'
  - cython-lint>=0.12.2 ; extra == 'dev'
  - rich-click ; extra == 'dev'
  - doit>=0.36.0 ; extra == 'dev'
  - pydevtool ; extra == 'dev'
  requires_python: '>=3.10'
- pypi: https://files.pythonhosted.org/packages/32/7a/1e9a31a5d07d1d3ed53f9cca128133f52fb898cc49196fe0a66a0b056c2d/sherpa_onnx-1.10.43-cp312-cp312-win_amd64.whl
  name: sherpa-onnx
  version: 1.10.43
  sha256: 9a11a6f9f505a3ccfc210397271e82b3acc93943a3000316c464feed10edd8b4
  requires_python: '>=3.6'
- pypi: https://files.pythonhosted.org/packages/48/77/a3771191d4bac619df7dc06db14a7b22dd0007548b71ee54a81f80e2d219/sherpa_onnx-1.10.43-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: sherpa-onnx
  version: 1.10.43
  sha256: 324cb6f678575c3d4b486ac52852b0286d6d37f37ef59f5bb22da981d79b2c8c
  requires_python: '>=3.6'
- pypi: https://files.pythonhosted.org/packages/b7/ce/149a00dd41f10bc29e5921b496af8b574d8413afcd5e30dfa0ed46c2cc5e/six-1.17.0-py2.py3-none-any.whl
  name: six
  version: 1.17.0
  sha256: 4721f391ed90541fddacab5acf947aa0d3dc7d27b2e1e8eda2be8970586c3274
  requires_python: '>=2.7,!=3.0.*,!=3.1.*,!=3.2.*'
- pypi: https://files.pythonhosted.org/packages/e9/44/75a9c9421471a6c4805dbf2356f7c181a29c1879239abab1ea2cc8f38b40/sniffio-1.3.1-py3-none-any.whl
  name: sniffio
  version: 1.3.1
  sha256: 2f6da418d1f1e0fddd844478f41680e794e6051915791a034ff65e5f100525a2
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/14/e9/6b761de83277f2f02ded7e7ea6f07828ec78e4b229b80e4ca55dd205b9dc/soundfile-0.13.1-py2.py3-none-win_amd64.whl
  name: soundfile
  version: 0.13.1
  sha256: 1e70a05a0626524a69e9f0f4dd2ec174b4e9567f4d8b6c11d38b5c289be36ee9
  requires_dist:
  - cffi>=1.0
  - numpy
- pypi: https://files.pythonhosted.org/packages/57/5e/70bdd9579b35003a489fc850b5047beeda26328053ebadc1fb60f320f7db/soundfile-0.13.1-py2.py3-none-manylinux_2_28_x86_64.whl
  name: soundfile
  version: 0.13.1
  sha256: 03267c4e493315294834a0870f31dbb3b28a95561b80b134f0bd3cf2d5f0e618
  requires_dist:
  - cffi>=1.0
  - numpy
- pypi: https://files.pythonhosted.org/packages/66/b7/4a1bc231e0681ebf339337b0cd05b91dc6a0d701fa852bb812e244b7a030/srt-3.5.3.tar.gz
  name: srt
  version: 3.5.3
  sha256: 4884315043a4f0740fd1f878ed6caa376ac06d70e135f306a6dc44632eed0cc0
  requires_python: '>=2.7'
- pypi: https://files.pythonhosted.org/packages/d9/61/f2b52e107b1fc8944b33ef56bf6ac4ebbe16d91b94d2b87ce013bf63fb84/starlette-0.45.3-py3-none-any.whl
  name: starlette
  version: 0.45.3
  sha256: dfb6d332576f136ec740296c7e8bb8c8a7125044e7c6da30744718880cdd059d
  requires_dist:
  - anyio>=3.6.2,<5
  - typing-extensions>=3.10.0 ; python_full_version < '3.10'
  - httpx>=0.27.0,<0.29.0 ; extra == 'full'
  - itsdangerous ; extra == 'full'
  - jinja2 ; extra == 'full'
  - python-multipart>=0.0.18 ; extra == 'full'
  - pyyaml ; extra == 'full'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/99/ff/c87e0622b1dadea79d2fb0b25ade9ed98954c9033722eb707053d310d4f3/sympy-1.13.3-py3-none-any.whl
  name: sympy
  version: 1.13.3
  sha256: 54612cf55a62755ee71824ce692986f23c88ffa77207b30c1368eda4a7060f73
  requires_dist:
  - mpmath>=1.1.0,<1.4
  - pytest>=7.1.0 ; extra == 'dev'
  - hypothesis>=6.70.0 ; extra == 'dev'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/40/44/4a5f08c96eb108af5cb50b41f76142f0afa346dfa99d5296fe7202a11854/tabulate-0.9.0-py3-none-any.whl
  name: tabulate
  version: 0.9.0
  sha256: 024ca478df22e9340661486f85298cff5f6dcdba14f3813e8830015b9ed1948f
  requires_dist:
  - wcwidth ; extra == 'widechars'
  requires_python: '>=3.7'
- conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_h4845f30_101.conda
  sha256: e0569c9caa68bf476bead1bed3d79650bb080b532c64a4af7d8ca286c08dea4e
  md5: d453b98d9c83e71da0741bb0ff4d76bc
  depends:
  - libgcc-ng >=12
  - libzlib >=1.2.13,<2.0.0a0
  license: TCL
  license_family: BSD
  purls: []
  size: 3318875
  timestamp: 1699202167581
- conda: https://conda.anaconda.org/conda-forge/win-64/tk-8.6.13-h5226925_1.conda
  sha256: 2c4e914f521ccb2718946645108c9bd3fc3216ba69aea20c2c3cedbd8db32bb1
  md5: fc048363eb8f03cd1737600a5d08aafe
  depends:
  - ucrt >=10.0.20348.0
  - vc >=14.2,<15
  - vc14_runtime >=14.29.30139
  license: TCL
  license_family: BSD
  purls: []
  size: 3503410
  timestamp: 1699202577803
- pypi: https://files.pythonhosted.org/packages/5c/51/51c3f2884d7bab89af25f678447ea7d297b53b5a3b5730a7cb2ef6069f07/tomli-2.2.1-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: tomli
  version: 2.2.1
  sha256: db2b95f9de79181805df90bedc5a5ab4c165e6ec3fe99f970d0e302f384ad222
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/ef/60/9b9638f081c6f1261e2688bd487625cd1e660d0a85bd469e91d8db969734/tomli-2.2.1-cp312-cp312-win_amd64.whl
  name: tomli
  version: 2.2.1
  sha256: 7fc04e92e1d624a4a63c76474610238576942d6b8950a2d7f908a340494e67e4
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/d0/30/dc54f88dd4a2b5dc8a0279bdd7270e735851848b762aeb1c1184ed1f6b14/tqdm-4.67.1-py3-none-any.whl
  name: tqdm
  version: 4.67.1
  sha256: 26445eca388f82e72884e0d580d5464cd801a3ea01e63e5601bdff9ba6a48de2
  requires_dist:
  - colorama ; platform_system == 'Windows'
  - pytest>=6 ; extra == 'dev'
  - pytest-cov ; extra == 'dev'
  - pytest-timeout ; extra == 'dev'
  - pytest-asyncio>=0.24 ; extra == 'dev'
  - nbval ; extra == 'dev'
  - requests ; extra == 'discord'
  - slack-sdk ; extra == 'slack'
  - requests ; extra == 'telegram'
  - ipywidgets>=6 ; extra == 'notebook'
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/26/9f/ad63fc0248c5379346306f8668cda6e2e2e9c95e01216d2b8ffd9ff037d0/typing_extensions-4.12.2-py3-none-any.whl
  name: typing-extensions
  version: 4.12.2
  sha256: 04e5ca0351e0f3f85c6853954072df659d0d13fac324d0072316b67d7794700d
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025a-h78e105d_0.conda
  sha256: c4b1ae8a2931fe9b274c44af29c5475a85b37693999f8c792dad0f8c6734b1de
  md5: dbcace4706afdfb7eb891f7b37d07c04
  license: LicenseRef-Public-Domain
  purls: []
  size: 122921
  timestamp: 1737119101255
- conda: https://conda.anaconda.org/conda-forge/win-64/ucrt-10.0.22621.0-h57928b3_1.conda
  sha256: db8dead3dd30fb1a032737554ce91e2819b43496a0db09927edf01c32b577450
  md5: 6797b005cd0f439c4c5c9ac565783700
  constrains:
  - vs2015_runtime >=14.29.30037
  license: LicenseRef-MicrosoftWindowsSDK10
  purls: []
  size: 559710
  timestamp: 1728377334097
- pypi: https://files.pythonhosted.org/packages/c8/19/4ec628951a74043532ca2cf5d97b7b14863931476d117c471e8e2b1eb39f/urllib3-2.3.0-py3-none-any.whl
  name: urllib3
  version: 2.3.0
  sha256: 1cee9ad369867bfdbbb48b7dd50374c0967a0bb7710050facf0dd6911440e3df
  requires_dist:
  - brotli>=1.0.9 ; platform_python_implementation == 'CPython' and extra == 'brotli'
  - brotlicffi>=0.8.0 ; platform_python_implementation != 'CPython' and extra == 'brotli'
  - h2>=4,<5 ; extra == 'h2'
  - pysocks>=1.5.6,!=1.5.7,<2.0 ; extra == 'socks'
  - zstandard>=0.18.0 ; extra == 'zstd'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/61/14/33a3a1352cfa71812a3a21e8c9bfb83f60b0011f5e36f2b1399d51928209/uvicorn-0.34.0-py3-none-any.whl
  name: uvicorn
  version: 0.34.0
  sha256: 023dc038422502fa28a09c7a30bf2b6991512da7dcdb8fd35fe57cfc154126f4
  requires_dist:
  - click>=7.0
  - h11>=0.8
  - typing-extensions>=4.0 ; python_full_version < '3.11'
  - colorama>=0.4 ; sys_platform == 'win32' and extra == 'standard'
  - httptools>=0.6.3 ; extra == 'standard'
  - python-dotenv>=0.13 ; extra == 'standard'
  - pyyaml>=5.1 ; extra == 'standard'
  - uvloop>=0.14.0,!=0.15.0,!=0.15.1 ; platform_python_implementation != 'PyPy' and sys_platform != 'cygwin' and sys_platform != 'win32' and extra == 'standard'
  - watchfiles>=0.13 ; extra == 'standard'
  - websockets>=10.4 ; extra == 'standard'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/06/a7/b4e6a19925c900be9f98bec0a75e6e8f79bb53bdeb891916609ab3958967/uvloop-0.21.0-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: uvloop
  version: 0.21.0
  sha256: 86975dca1c773a2c9864f4c52c5a55631038e387b47eaf56210f873887b6c8dc
  requires_dist:
  - setuptools>=60 ; extra == 'dev'
  - cython~=3.0 ; extra == 'dev'
  - sphinx~=4.1.2 ; extra == 'docs'
  - sphinxcontrib-asyncio~=0.3.0 ; extra == 'docs'
  - sphinx-rtd-theme~=0.5.2 ; extra == 'docs'
  - aiohttp>=3.10.5 ; extra == 'test'
  - flake8~=5.0 ; extra == 'test'
  - psutil ; extra == 'test'
  - pycodestyle~=2.9.0 ; extra == 'test'
  - pyopenssl~=23.0.0 ; extra == 'test'
  - mypy>=0.800 ; extra == 'test'
  requires_python: '>=3.8.0'
- conda: https://conda.anaconda.org/conda-forge/win-64/vc-14.3-h5fd82a7_24.conda
  sha256: 7ce178cf139ccea5079f9c353b3d8415d1d49b0a2f774662c355d3f89163d7b4
  md5: 00cf3a61562bd53bd5ea99e6888793d0
  depends:
  - vc14_runtime >=14.40.33810
  track_features:
  - vc14
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 17693
  timestamp: 1737627189024
- conda: https://conda.anaconda.org/conda-forge/win-64/vc14_runtime-14.42.34433-h6356254_24.conda
  sha256: abda97b8728cf6e3c37df8f1178adde7219bed38b96e392cb3be66336386d32e
  md5: 2441e010ee255e6a38bf16705a756e94
  depends:
  - ucrt >=10.0.20348.0
  constrains:
  - vs2015_runtime 14.42.34433.* *_24
  license: LicenseRef-MicrosoftVisualCpp2015-2022Runtime
  license_family: Proprietary
  purls: []
  size: 753531
  timestamp: 1737627061911
- conda: https://conda.anaconda.org/conda-forge/win-64/vs2015_runtime-14.42.34433-hfef2bbc_24.conda
  sha256: 09102e0bd283af65772c052d85028410b0c31989b3cd96c260485d28e270836e
  md5: 117fcc5b86c48f3b322b0722258c7259
  depends:
  - vc14_runtime >=14.42.34433
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 17669
  timestamp: 1737627066773
- pypi: https://files.pythonhosted.org/packages/2b/b4/9396cc61b948ef18943e7c85ecfa64cf940c88977d882da57147f62b34b1/watchfiles-1.0.4-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: watchfiles
  version: 1.0.4
  sha256: 5c11ea22304d17d4385067588123658e9f23159225a27b983f343fcffc3e796a
  requires_dist:
  - anyio>=3.0.0
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/ea/94/b0165481bff99a64b29e46e07ac2e0df9f7a957ef13bec4ceab8515f44e3/watchfiles-1.0.4-cp312-cp312-win_amd64.whl
  name: watchfiles
  version: 1.0.4
  sha256: c2acfa49dd0ad0bf2a9c0bb9a985af02e89345a7189be1efc6baa085e0f72d7c
  requires_dist:
  - anyio>=3.0.0
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/5a/84/44687a29792a70e111c5c477230a72c4b957d88d16141199bf9acb7537a3/websocket_client-1.8.0-py3-none-any.whl
  name: websocket-client
  version: 1.8.0
  sha256: 17b44cc997f5c498e809b22cdf2d9c7a9e71c02c8cc2b6c56e7c2d1239bfa526
  requires_dist:
  - sphinx>=6.0 ; extra == 'docs'
  - sphinx-rtd-theme>=1.1.0 ; extra == 'docs'
  - myst-parser>=2.0.0 ; extra == 'docs'
  - python-socks ; extra == 'optional'
  - wsaccel ; extra == 'optional'
  - websockets ; extra == 'test'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/81/da/72f7caabd94652e6eb7e92ed2d3da818626e70b4f2b15a854ef60bf501ec/websockets-14.2-cp312-cp312-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: websockets
  version: '14.2'
  sha256: a39d7eceeea35db85b85e1169011bb4321c32e673920ae9c1b6e0978590012a3
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/b3/7d/32cdb77990b3bdc34a306e0a0f73a1275221e9a66d869f6ff833c95b56ef/websockets-14.2-cp312-cp312-win_amd64.whl
  name: websockets
  version: '14.2'
  sha256: 44bba1a956c2c9d268bdcdf234d5e5ff4c9b6dc3e300545cbe99af59dda9dcce
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/e1/07/c6fe3ad3e685340704d314d765b7912993bcb8dc198f0e7a89382d37974b/win32_setctime-1.2.0-py3-none-any.whl
  name: win32-setctime
  version: 1.2.0
  sha256: 95d644c4e708aba81dc3704a116d8cbc974d70b3bdb8be1d150e36be6e9d1390
  requires_dist:
  - black>=19.3b0 ; python_full_version >= '3.6' and extra == 'dev'
  - pytest>=4.6.2 ; extra == 'dev'
  requires_python: '>=3.5'
- pypi: https://files.pythonhosted.org/packages/1a/e1/a097d5755d3ea8479a42856f51d97eeff7a3a7160593332d98f2709b3580/yarl-1.18.3-cp312-cp312-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: yarl
  version: 1.18.3
  sha256: 00e5a1fea0fd4f5bfa7440a47eff01d9822a65b4488f7cff83155a0f31a2ecba
  requires_dist:
  - idna>=2.0
  - multidict>=4.0
  - propcache>=0.2.0
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/34/45/0e055320daaabfc169b21ff6174567b2c910c45617b0d79c68d7ab349b02/yarl-1.18.3-cp312-cp312-win_amd64.whl
  name: yarl
  version: 1.18.3
  sha256: 7e2ee16578af3b52ac2f334c3b1f92262f47e02cc6193c598502bd46f5cd1477
  requires_dist:
  - idna>=2.0
  - multidict>=4.0
  - propcache>=0.2.0
  requires_python: '>=3.9'
