<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ASR & TTS Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }

        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 8px;
            background-color: #f9f9f9;
        }

        .controls {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
        }

        .record-controls,
        .upload-controls {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .file-input {
            max-width: 200px;
        }

        .button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            background-color: #007bff;
            color: white;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        .button:hover {
            background-color: #0056b3;
        }

        .button:disabled {
            background-color: #cccccc;
            cursor: not-allowed;
        }

        textarea {
            width: 100%;
            height: 100px;
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            resize: vertical;
        }

        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
            font-size: 14px;
        }

        .error {
            background-color: #ffe6e6;
            border: 1px solid #ffcccc;
            color: #cc0000;
        }

        .success {
            background-color: #e6ffe6;
            border: 1px solid #ccffcc;
            color: #006600;
        }

        #audioPlayer {
            width: 100%;
            margin: 10px 0;
        }

        h1,
        h2 {
            color: #333;
        }

        h1 {
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 30px;
        }
    </style>
</head>

<body>
    <h1>ASR & TTS Tool</h1>

    <div class="section">
        <h2>Speech Recognition</h2>
        <div class="controls">
            <div class="record-controls">
                <button id="startRecording" class="button">Start Recording</button>
                <button id="stopRecording" class="button" disabled>Stop Recording</button>
            </div>
            <div class="upload-controls">
                <input type="file" id="audioFileInput" accept="audio/*"
                    title="Supported formats: WAV, MP3, M4A, OGG, etc. Files will be converted to 16kHz mono WAV."
                    class="file-input" />
                <button id="uploadAudio" class="button">Upload Audio</button>
            </div>
        </div>
        <div id="asrStatus" class="status"></div>
        <textarea id="transcription" placeholder="Transcription will appear here..." readonly></textarea>
    </div>

    <div class="section">
        <h2>Text to Speech</h2>
        <textarea id="ttsInput" placeholder="Enter text for TTS..."></textarea>
        <button id="generateSpeech" class="button">Generate Speech</button>
        <div id="ttsStatus" class="status"></div>
        <audio id="audioPlayer" controls></audio>
        <button id="downloadAudio" class="button" disabled>Download Audio</button>
    </div>

    <script src="recorder.js"></script>
    <script src="main.js"></script>
</body>

</html>